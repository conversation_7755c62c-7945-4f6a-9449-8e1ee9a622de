import { Check<PERSON>, <PERSON>over, Spin, Tooltip } from "antd";
import React, { useCallback, useEffect, useState } from "react";

import { BuildInCommand, get, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface KnowledgeBaseItem {
  id: string;
  knowledge_name: string;
}

const KnowledgeBaseSelect: React.FC = () => {
  const runner = useCommandRunner();
  const [selectedKnowledgeBaseList, setSelectedKnowledgeBaseList] = useState<string[]>([]);
  const [knowledgeBaseOptions, setKnowledgeBaseOptions] = useState<{ label: string; value: string }[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchKnowledgeBaseList = useCallback(async () => {
    try {
      setLoading(true);
      const response = await get<{
        code: number;
        data: KnowledgeBaseItem[];
        message: string;
      }>("/knowledge-base/list");

      if (response.data?.code === 200 && response.data?.data) {
        const options = response.data.data.map((item: KnowledgeBaseItem) => ({
          label: item.knowledge_name,
          value: item.id,
        }));
        setKnowledgeBaseOptions(options);
      }
    } catch (error) {
      console.error("获取知识库列表失败:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchKnowledgeBaseList();
  }, [fetchKnowledgeBaseList]);

  const handleChange = (checkedValues: string[]) => {
    setSelectedKnowledgeBaseList(checkedValues);
    runner(BuildInCommand.UpdateChatKnowledgeBaseList, {
      setValue: () => {
        return [...checkedValues];
      },
    });
  };

  const listContent = () => {
    return (
      <Spin spinning={loading}>
        <div className="pts:-mx-4 pts:-my-2 pts:w-44 pts:max-h-40 pts:overflow-x-hidden pts:overflow-y-auto mini-scrollbar">
          <Checkbox.Group
            value={selectedKnowledgeBaseList}
            onChange={(checkedValues: any[]) => handleChange(checkedValues)}
          >
            {knowledgeBaseOptions.map((i) => (
              <div key={i.value} className="pts:hover:bg-[rgba(49,186,244,0.1)] pts:px-4 pts:py-1.5">
                <Checkbox value={i.value}>
                  <div
                    title={i.label}
                    className="pts:w-35 pts:overflow-ellipsis pts:overflow-hidden pts:whitespace-nowrap"
                  >
                    {i.label}
                  </div>
                </Checkbox>
              </div>
            ))}
          </Checkbox.Group>
        </div>
      </Spin>
    );
  };

  return (
    <Tooltip title="知识库">
      <Popover placement="bottom" title={null} content={listContent} trigger="click">
        <div className="pts:relative pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:rounded-lg pts:w-8 pts:h-8 pts:cursor-pointer">
          <Icon icon="KnowledgeBase" />
        </div>
      </Popover>
    </Tooltip>
  );
};

export default KnowledgeBaseSelect;
