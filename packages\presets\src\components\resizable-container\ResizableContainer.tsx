import { usePrevious } from "ahooks";
import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useRef,
  useCallback,
  useEffect,
  type ReactNode,
} from "react";

interface ResizableContainerProps {
  // 两列布局属性
  leftContent: ReactNode;
  rightContent: ReactNode;
  initialLeftWidth?: string; // 支持像素值或百分比，默认 35%
  minLeftWidth?: string; // 最小左列宽度（像素），默认 400px
  minRightWidth?: string; // 最大左列宽度（像素），默认 400px
  height?: number;
  className?: string;
  disabled?: boolean;
  hideRightContent?: boolean;
}

function transformToPixel(value: string, containerWidth: number): number {
  if (value.endsWith("px")) {
    return parseInt(value.replace("px", ""));
  }
  if (value.endsWith("%")) {
    return (parseFloat(value.replace("%", "")) / 100) * containerWidth;
  }
  return 0;
}

const ResizableContainer = forwardRef<
  {
    reLayout: () => void;
  },
  ResizableContainerProps
>(function ResizableContainerComponent(props, ref) {
  const {
    leftContent,
    rightContent,
    initialLeftWidth = "35%",
    minLeftWidth = "400px",
    minRightWidth = "400px",
    className,
    disabled = false,
    hideRightContent = false,
  } = props;
  // 状态管理

  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startLeftWidthRef = useRef(0);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [fixedContainerWidth, setFixedContainerWidth] = useState(false);
  const [leftWidth, setLeftWidth] = useState(initialLeftWidth);
  const prevHideRightContent = usePrevious(hideRightContent)

  const reLayout = (_leftWidth?: string) => {
    if (hideRightContent) {
      setLeftWidth("100%");
      return;
    }

    if (_leftWidth) {
      setLeftWidth(transformToPixel(_leftWidth, containerWidth) + "px");
      return;
    }

    if (containerWidth - transformToPixel(leftWidth, containerWidth) < transformToPixel(minLeftWidth, containerWidth)) {
      setLeftWidth(containerWidth - transformToPixel(minRightWidth, containerWidth) + "px");
    }
  };

  useEffect(() => {
    if (!hideRightContent && prevHideRightContent) {
      reLayout(initialLeftWidth);
    } else {
      reLayout();
    }
  }, [containerWidth, hideRightContent, prevHideRightContent]);

  useEffect(() => {
    if (!containerRef.current) return;
    // 监听容器宽度变化，重新修改宽度
    const resizeObserver = new ResizeObserver(() => {
      setContainerWidth(containerRef.current?.offsetWidth ?? 0);
    });
    resizeObserver.observe(containerRef.current);

    return () => {
      resizeObserver.disconnect();
    };
  }, [hideRightContent]);

  useImperativeHandle(ref, () => {
    return {
      reLayout,
    };
  });

  // 鼠标移动处理
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing) return;
      const deltaX = e.clientX - startXRef.current;

      const minRightWidthPx = transformToPixel(minRightWidth + "px", containerWidth);
      const minLeftWidthPx = transformToPixel(minLeftWidth + "px", containerWidth);

      // 计算新的左列宽度（像素值）
      const newLeftWidthPx =
        Math.max(minLeftWidthPx, Math.min(containerWidth - minRightWidthPx, startLeftWidthRef.current + deltaX)) -
        1 +
        "px";

      setLeftWidth(newLeftWidthPx);
    },
    [minLeftWidth, minRightWidth, containerWidth, isResizing],
  );

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    setTimeout(() => {
      setFixedContainerWidth(false);
      document.body.style.userSelect = "";
      document.body.style.cursor = "";
      document.body.style.overflow = "";
    }, 200);
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // 鼠标按下处理
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (disabled) return;
      setContainerWidth(containerRef.current?.offsetWidth ?? 0);
      e.preventDefault();
      if (!containerRef.current) return;

      setIsResizing(true);
      setFixedContainerWidth(true);
      startXRef.current = e.clientX;

      // 计算当前左列的像素宽度
      const currentLeftWidthPx = leftWidth.endsWith("%")
        ? (parseFloat(leftWidth.replace("%", "")) / 100) * containerRef.current.offsetWidth
        : parseInt(leftWidth.replace("px", ""));
      startLeftWidthRef.current = currentLeftWidthPx;

      // 防止文本选择
      document.body.style.userSelect = "none";
      document.body.style.cursor = "col-resize";
      document.body.style.overflow = "hidden";
    },
    [leftWidth, disabled],
  );

  return (
    <div
      ref={containerRef}
      className={`pts:relative pts:bg-card pts:overflow-hidden pts:flex pts:h-full ${className}`}
      style={{
        width: fixedContainerWidth ? containerWidth + "px" : "",
      }}
    >
      {/* 左列 */}
      <div
        className={`pts:h-full ${isResizing ? "pts:pointer-events-none" : ""}`}
        style={{
          width: `${leftWidth}`,
          maxWidth: `${leftWidth}`,
          transition: isResizing ? "none" : "width 0.2s ease-out",
        }}
      >
        {leftContent}
      </div>

      {/* 扩展的拖拽区域，便于用户操作 */}
      <div
        className="pts:relative pts:bg-[rgba(37,45,62,0.25)] pts:w-[1px] pts:min-w-[1px] pts:max-w-[1px] pts:h-full pts:cursor-col-resize"
        style={{
          display: disabled ? "none" : "block",
        }}
        onMouseDown={handleMouseDown}
      >
        <div className="pts:top-0 pts:-left-0.5 pts:absolute pts:w-1 pts:h-full"></div>
      </div>

      {/* 右列 */}
      <div
        className={`pts:h-full pts:flex-1 pts:overflow-hidden ${isResizing ? "pts:pointer-events-none" : ""}`}
        style={{
          transition: isResizing ? "none" : "width 0.2s ease-out",
          position: "relative",
          display: hideRightContent ? "none" : "block",
        }}
      >
        <div className="pts:top-0 pts:left-0 pts:absolute pts:w-full pts:h-full pts:overflow-hidden">
          {rightContent}
        </div>
      </div>
    </div>
  );
});

export default ResizableContainer;
