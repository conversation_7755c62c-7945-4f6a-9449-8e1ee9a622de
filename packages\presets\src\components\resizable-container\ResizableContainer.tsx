"use client";

import React from "react";

import { useState, useRef, useCallback, useEffect, type ReactNode } from "react";

interface ResizableContainerProps {
  children: ReactNode;
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  height?: number;
  className?: string;
  handlePosition?: "left" | "right";
}

const ResizableContainer: React.FC<ResizableContainerProps> = ({
  children,
  initialWidth = 300,
  minWidth = 100,
  maxWidth = 1000,
  height = "",
  className,
  handlePosition = "left",
}) => {
  const [width, setWidth] = useState(initialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      const deltaX = e.clientX - startXRef.current;
      // 当拖拽手柄在左侧时，需要反转拖拽方向
      const adjustedDelta = handlePosition === "left" ? -deltaX : deltaX;
      const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidthRef.current + adjustedDelta));
      setWidth(newWidth);
    },
    [minWidth, maxWidth, handlePosition],
  );

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = "";
    document.body.style.cursor = "";
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);
      startXRef.current = e.clientX;
      startWidthRef.current = width;

      // 防止文本选择
      document.body.style.userSelect = "none";
      document.body.style.cursor = "col-resize";
    },
    [width],
  );

  return (
    <div
      ref={containerRef}
      className={`pts:relative pts:bg-card pts:border pts:border-border pts:rounded-lg pts:overflow-hidden ${className}`}
      style={{
        width: `${width}px`,
        height: height ? `${height}:px` : "auto",
        transition: isResizing ? "none" : "width 0.1s ease-out",
      }}
    >
      {/* 主要内容区域 */}
      <div className={`pts:h-full pts:overflow-auto ${isResizing && "pts:pointer-events-none"}`}>{children}</div>

      {/* 拖拽手柄 */}
      <div
        className={`pts:group pts:top-0 ${handlePosition === "left" ? "pts:left-0" : "pts:right-0"} pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-transparent hover:pts:bg-primary/20 pts:w-1 pts:h-full pts:transition-colors pts:cursor-col-resize ${isResizing && "pts:bg-primary/30"}`}
        onMouseDown={handleMouseDown}
      >
        {/* 可视化拖拽指示器 */}
        <div
          className={`pts:opacity-0 group-hover:pts:opacity-100 pts:bg-border pts:rounded-full pts:w-0.5 pts:h-8 pts:transition-opacity ${isResizing && "pts:opacity-100 pts:bg-primary"}`}
        />
      </div>

      {/* 扩展的拖拽区域，便于用户操作 */}
      <div
        className={`pts:top-0 ${handlePosition === "left" ? "pts:left-0" : "pts:right-0"} pts:absolute pts:bg-transparent pts:w-3 pts:h-full pts:cursor-col-resize`}
        onMouseDown={handleMouseDown}
      />
    </div>
  );
};

export default ResizableContainer;
