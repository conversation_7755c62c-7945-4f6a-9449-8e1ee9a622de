"use client";

import React from "react";

import { useState, useRef, useCallback, useEffect, type ReactNode } from "react";

interface ResizableContainerProps {
  // 向后兼容的单列模式属性
  children?: ReactNode;
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  height?: number;
  className?: string;
  handlePosition?: "left" | "right";

  // 新的两列布局属性
  leftContent?: ReactNode;
  rightContent?: ReactNode;
  initialLeftWidth?: number | string; // 支持像素值或百分比
  minLeftWidth?: number;
  maxLeftWidth?: number;

  // 保留旧的属性名以确保向后兼容
  leftCol?: React.ReactNode;
  rightCol?: React.ReactNode;
}

const ResizableContainer: React.FC<ResizableContainerProps> = ({
  // 向后兼容属性
  children,
  initialWidth = 300,
  minWidth = 100,
  maxWidth = 1000,
  height = "",
  className,
  handlePosition = "left",

  // 新的两列布局属性
  leftContent,
  rightContent,
  initialLeftWidth = "50%",
  minLeftWidth = 200,
  maxLeftWidth = 800,

}) => {
  // 判断是否使用两列模式
  const isTwoColumnMode = !!(leftContent || rightContent);

  // 状态管理
  const [leftWidth, setLeftWidth] = useState(() => {
    if (typeof initialLeftWidth === "string" && initialLeftWidth.endsWith("%")) {
      return parseFloat(initialLeftWidth);
    }
    return typeof initialLeftWidth === "number" ? initialLeftWidth : 300;
  });
  const [width, setWidth] = useState(initialWidth);
  const [isResizing, setIsResizing] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const dividerRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);
  const startLeftWidthRef = useRef(0);

  // 两列模式的鼠标移动处理
  const handleTwoColumnMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const containerWidth = containerRect.width;
      const deltaX = e.clientX - startXRef.current;

      // 计算新的左列宽度（像素值）
      const newLeftWidthPx = Math.max(minLeftWidth, Math.min(maxLeftWidth, startLeftWidthRef.current + deltaX));

      // 转换为百分比
      const newLeftWidthPercent = (newLeftWidthPx / containerWidth) * 100;
      setLeftWidth(newLeftWidthPercent);
    },
    [minLeftWidth, maxLeftWidth],
  );

  // 单列模式的鼠标移动处理（向后兼容）
  const handleSingleColumnMouseMove = useCallback(
    (e: MouseEvent) => {
      const deltaX = e.clientX - startXRef.current;
      // 当拖拽手柄在左侧时，需要反转拖拽方向
      const adjustedDelta = handlePosition === "left" ? -deltaX : deltaX;
      const newWidth = Math.max(minWidth, Math.min(maxWidth, startWidthRef.current + adjustedDelta));
      setWidth(newWidth);
    },
    [minWidth, maxWidth, handlePosition],
  );

  // 根据模式选择合适的处理函数
  const handleMouseMove = isTwoColumnMode ? handleTwoColumnMouseMove : handleSingleColumnMouseMove;

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = "";
    document.body.style.cursor = "";
  }, []);

  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // 两列模式的鼠标按下处理
  const handleTwoColumnMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      if (!containerRef.current) return;

      setIsResizing(true);
      startXRef.current = e.clientX;

      // 计算当前左列的像素宽度
      const containerRect = containerRef.current.getBoundingClientRect();
      const currentLeftWidthPx = (leftWidth / 100) * containerRect.width;
      startLeftWidthRef.current = currentLeftWidthPx;

      // 防止文本选择
      document.body.style.userSelect = "none";
      document.body.style.cursor = "col-resize";
    },
    [leftWidth],
  );

  // 单列模式的鼠标按下处理（向后兼容）
  const handleSingleColumnMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      setIsResizing(true);
      startXRef.current = e.clientX;
      startWidthRef.current = width;

      // 防止文本选择
      document.body.style.userSelect = "none";
      document.body.style.cursor = "col-resize";
    },
    [width],
  );

  // 根据模式选择合适的处理函数
  const handleMouseDown = isTwoColumnMode ? handleTwoColumnMouseDown : handleSingleColumnMouseDown;

  // 键盘无障碍支持
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (!isTwoColumnMode) return;

      const step = 5; // 每次调整5%
      let newLeftWidth = leftWidth;

      switch (e.key) {
        case "ArrowLeft":
          e.preventDefault();
          newLeftWidth = Math.max(10, leftWidth - step); // 最小10%
          break;
        case "ArrowRight":
          e.preventDefault();
          newLeftWidth = Math.min(90, leftWidth + step); // 最大90%
          break;
        case "Home":
          e.preventDefault();
          newLeftWidth = 20; // 重置到20%
          break;
        case "End":
          e.preventDefault();
          newLeftWidth = 80; // 重置到80%
          break;
        default:
          return;
      }

      setLeftWidth(newLeftWidth);
    },
    [isTwoColumnMode, leftWidth],
  );

  // 渲染两列布局
  if (isTwoColumnMode) {
    return (
      <div
        ref={containerRef}
        className={`pts:relative pts:bg-card pts:border pts:border-border pts:rounded-lg pts:overflow-hidden pts:flex ${className}`}
        style={{
          height: height ? `${height}px` : "auto",
        }}
      >
        {/* 左列 */}
        <div
          className={`pts:h-full pts:overflow-auto ${isResizing && "pts:pointer-events-none"}`}
          style={{
            width: `${leftWidth}%`,
            transition: isResizing ? "none" : "width 0.2s ease-out",
          }}
        >
          {leftContent}
        </div>

        {/* 分隔线/拖拽手柄 */}
        <div
          ref={dividerRef}
          className={`pts:group pts:relative pts:flex pts:items-center pts:justify-center pts:bg-border/30 hover:pts:bg-primary/20 pts:w-1 pts:h-full pts:transition-colors pts:cursor-col-resize pts:flex-shrink-0 ${isResizing && "pts:bg-primary/30"}`}
          onMouseDown={handleMouseDown}
          onKeyDown={handleKeyDown}
          tabIndex={0}
          role="separator"
          aria-orientation="vertical"
          aria-label="调整左右列宽度比例"
          aria-valuenow={Math.round(leftWidth)}
          aria-valuemin={10}
          aria-valuemax={90}
          aria-valuetext={`左列宽度 ${Math.round(leftWidth)}%`}
        >
          {/* 可视化拖拽指示器 */}
          <div
            className={`pts:opacity-0 group-hover:pts:opacity-100 pts:bg-border pts:rounded-full pts:w-0.5 pts:h-8 pts:transition-opacity ${isResizing && "pts:opacity-100 pts:bg-primary"}`}
          />

          {/* 聚焦时的视觉指示器 */}
          <div className="pts:absolute pts:inset-0 focus-within:pts:ring-opacity-50 pts:ring-opacity-0 pts:rounded pts:ring-2 pts:ring-primary pts:transition-all" />
        </div>

        {/* 右列 */}
        <div
          className={`pts:h-full pts:overflow-auto pts:flex-1 ${isResizing && "pts:pointer-events-none"}`}
          style={{
            transition: isResizing ? "none" : "width 0.2s ease-out",
          }}
        >
          {finalRightContent}
        </div>

        {/* 扩展的拖拽区域，便于用户操作 */}
        <div
          className="pts:top-0 pts:z-10 pts:absolute pts:bg-transparent pts:w-3 pts:h-full pts:cursor-col-resize"
          style={{
            left: `calc(${leftWidth}% - 1.5px)`,
          }}
          onMouseDown={handleMouseDown}
        />
      </div>
    );
  }

  // 向后兼容的单列模式
  return (
    <div
      ref={containerRef}
      className={`pts:relative pts:bg-card pts:border pts:border-border pts:rounded-lg pts:overflow-hidden ${className}`}
      style={{
        width: `${width}px`,
        height: height ? `${height}px` : "auto",
        transition: isResizing ? "none" : "width 0.1s ease-out",
      }}
    >
      {/* 主要内容区域 */}
      <div className={`pts:h-full pts:overflow-auto ${isResizing && "pts:pointer-events-none"}`}>{children}</div>

      {/* 拖拽手柄 */}
      <div
        className={`pts:group pts:top-0 ${handlePosition === "left" ? "pts:left-0" : "pts:right-0"} pts:absolute pts:flex pts:justify-center pts:items-center pts:bg-transparent hover:pts:bg-primary/20 pts:w-1 pts:h-full pts:transition-colors pts:cursor-col-resize ${isResizing && "pts:bg-primary/30"}`}
        onMouseDown={handleMouseDown}
      >
        {/* 可视化拖拽指示器 */}
        <div
          className={`pts:opacity-0 group-hover:pts:opacity-100 pts:bg-border pts:rounded-full pts:w-0.5 pts:h-8 pts:transition-opacity ${isResizing && "pts:opacity-100 pts:bg-primary"}`}
        />
      </div>

      {/* 扩展的拖拽区域，便于用户操作 */}
      <div
        className={`pts:top-0 ${handlePosition === "left" ? "pts:left-0" : "pts:right-0"} pts:absolute pts:bg-transparent pts:w-3 pts:h-full pts:cursor-col-resize`}
        onMouseDown={handleMouseDown}
      />
    </div>
  );
};

export default ResizableContainer;
