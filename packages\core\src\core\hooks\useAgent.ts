import React, { useContext, useMemo } from "react";

import { AgentChatContext } from "@/core/state/context";

import { useActiveAgentCode } from "../state/hooks";

export const useAgentConfigs = () => {
  const { config } = React.useContext(AgentChatContext);

  return config?.agents ?? [];
};

export const useConfig = () => {
  const { config } = React.useContext(AgentChatContext);

  return config ?? {};
};

export const useActiveAgentConfig = () => {
  const agents = useAgentConfigs();
  const [activeAgentCode] = useActiveAgentCode();

  const activeAgentConfig = useMemo(() => {
    return agents.find((i) => i.code === activeAgentCode);
  }, [agents, activeAgentCode]);

  return activeAgentConfig;
};

export const useCurrentAgentAccessible = () => {
  const [activeAgentCode] = useActiveAgentCode();
  const { config } = useContext(AgentChatContext);
  const agents = config.agents;
  const accessible = useMemo(() => agents && agents.some((i) => i.code === activeAgentCode), [activeAgentCode, agents]);

  return accessible;
};
