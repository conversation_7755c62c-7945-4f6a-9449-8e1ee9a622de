import path from "path";

import { UserConfig } from "vite";
import { mockDevServerPlugin } from "vite-plugin-mock-dev-server";

import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";
import react from "@vitejs/plugin-react";

import appDepInfoPlugin from "../vite-plugins/vite-dependency-info-plugin.js";

// https://vite.dev/config/
const baseConfig: UserConfig = {
  base: "/",
  plugins: [
    mockDevServerPlugin(),
    react({ jsxRuntime: "classic" }),
    (typescript as any)(),
    tailwindcss(),
    appDepInfoPlugin(),
  ],
  build: {
    sourcemap: false,
  },
  resolve: {
    extensions: [".js", ".ts", ".jsx", ".tsx", ".json"],
    alias: {
      "@": path.resolve(process.cwd(), "src"),
      "@@": path.resolve(process.cwd(), "node_modules"),
    },
  },
  esbuild: {
    loader: "tsx",
    include: /\.(ts|tsx|jsx)$/,
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
};

export default baseConfig;
