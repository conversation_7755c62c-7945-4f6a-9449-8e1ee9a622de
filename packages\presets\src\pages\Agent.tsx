import React, { useContext, useEffect } from "react";

import { NavigationBar } from "@/components";
import { DefaultAgentLayout } from "@/layout";
import {
  AgentChatContext,
  BuildInCommand,
  Sender,
  useActiveAgentCode,
  useActiveAgentMenuCode,
  useCommandRunner,
  useConversationPreState,
  useNavigate,
  useParams,
  useSubscribeCommand,
} from "@cscs-agent/core";

const AgentHome: React.FC = () => {
  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const params = useParams();
  const { config } = useContext(AgentChatContext);
  const runner = useCommandRunner();
  const navigate = useNavigate();
  const { clearState } = useConversationPreState();

  useSubscribeCommand(BuildInCommand.NavigateTo, (params) => {
    const { path, options = {} } = params;
    navigate(path, options);
  });

  useEffect(() => {
    // 清理前置状态
    clearState();
    return () => {
      clearState();
    };
  }, [params.code]);

  useEffect(() => {
    if (params.code === activeAgentMenuCode) return;
    const agents = config.agents ?? [];
    const code = agents.find((i) => i.code === params.code)?.code;
    if (code) {
      setActiveAgentMenuCode(code);
      setActiveAgentCode(code);
    }
  }, [params, config.agents]);

  useEffect(() => {
    const search = new URLSearchParams(window.location.search);
    const sendMessage = search.get("sendMessage");
    if (sendMessage) {
      setTimeout(() => {
        runner(BuildInCommand.SendMessage, { message: sendMessage, isNewConversation: true, agentCode: params.code });
      }, 100);
    }
    return () => {
      setActiveAgentMenuCode(null);
      setActiveAgentCode(null);
    };
  }, []);

  return <DefaultAgentLayout sender={<Sender isNewConversation={true} />} navigationBar={<NavigationBar />} />;
};

export default AgentHome;
