import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useMemo } from "react";

import { PresetsCommand } from "@/command";
import { DefaultAgentLayoutContext } from "@/layout/Basic";
import {
  BuildInCommand,
  useActiveAgentCode,
  useAgentConfigs,
  useCommandRunner,
  useCurrentAgentAccessible,
} from "@cscs-agent/core";
import { useNavigate } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const NavigationBar: React.FC = () => {
  const [currentAgentCode] = useActiveAgentCode();
  const agentConfigs = useAgentConfigs();
  const { sideBarOpen } = React.useContext(DefaultAgentLayoutContext);
  const runner = useCommandRunner();
  const navigate = useNavigate();
  const accessible = useCurrentAgentAccessible();

  const name = useMemo(() => {
    return agentConfigs.find((i) => i.code === currentAgentCode)?.name;
  }, [agentConfigs, currentAgentCode]);

  const openSideBar = () => {
    runner(PresetsCommand.OpenSideBar);
  };

  const goAgentHome = () => {
    navigate(`/agent/${currentAgentCode}`);
    runner(BuildInCommand.ClearSender);
  };

  return (
    <div className="pts:flex pts:items-center pts:px-6 pts:py-3 pts:h-12 pts:text-[rgba(37,45,62,0.85)]">
      {!sideBarOpen && (
        <Button
          icon={<Icon icon="SideBar" />}
          type="text"
          size="small"
          onClick={openSideBar}
          className="pts:mr-4"
        ></Button>
      )}
      {accessible && (
        <>
          <span className="pts:mr-2 pts:font-bold pts:text-base">{name}</span>
          <Tooltip title="新建对话">
            <Button
              type="text"
              size="small"
              icon={<Icon icon="NewChat" className="pts:text-base! pts:text-black-65" />}
              onClick={goAgentHome}
            ></Button>
          </Tooltip>
        </>
      )}
    </div>
  );
};

export default NavigationBar;
