import React, { useState } from 'react';
import { ResizableContainer } from '@cscs-agent/presets';

const ResizableContainerDemo: React.FC = () => {
  const [leftHandlePosition, setLeftHandlePosition] = useState<'left' | 'right'>('left');
  const [rightHandlePosition, setRightHandlePosition] = useState<'left' | 'right'>('right');

  return (
    <div className="pts:p-8 pts:space-y-8">
      <div className="pts:text-center">
        <h1 className="pts:text-3xl pts:font-bold pts:mb-4">ResizableContainer 演示</h1>
        <p className="pts:text-gray-600 pts:mb-8">
          展示可配置拖拽手柄位置的 ResizableContainer 组件功能
        </p>
      </div>

      {/* 控制面板 */}
      <div className="pts:bg-gray-50 pts:p-4 pts:rounded-lg">
        <h2 className="pts:text-lg pts:font-semibold pts:mb-4">控制面板</h2>
        <div className="pts:grid pts:grid-cols-2 pts:gap-4">
          <div>
            <label className="pts:block pts:text-sm pts:font-medium pts:mb-2">
              左侧容器手柄位置:
            </label>
            <select
              value={leftHandlePosition}
              onChange={(e) => setLeftHandlePosition(e.target.value as 'left' | 'right')}
              className="pts:w-full pts:p-2 pts:border pts:border-gray-300 pts:rounded"
            >
              <option value="left">左侧</option>
              <option value="right">右侧</option>
            </select>
          </div>
          <div>
            <label className="pts:block pts:text-sm pts:font-medium pts:mb-2">
              右侧容器手柄位置:
            </label>
            <select
              value={rightHandlePosition}
              onChange={(e) => setRightHandlePosition(e.target.value as 'left' | 'right')}
              className="pts:w-full pts:p-2 pts:border pts:border-gray-300 pts:rounded"
            >
              <option value="left">左侧</option>
              <option value="right">右侧</option>
            </select>
          </div>
        </div>
      </div>

      {/* 演示区域 */}
      <div className="pts:flex pts:gap-4 pts:h-96">
        {/* 左侧容器 */}
        <ResizableContainer
          height={384}
          initialWidth={300}
          minWidth={200}
          maxWidth={500}
          handlePosition={leftHandlePosition}
          className="pts:bg-blue-50"
        >
          <div className="pts:p-4 pts:h-full">
            <h3 className="pts:text-lg pts:font-semibold pts:mb-2 pts:text-blue-800">
              左侧容器
            </h3>
            <p className="pts:text-blue-600 pts:mb-4">
              手柄位置: <strong>{leftHandlePosition === 'left' ? '左侧' : '右侧'}</strong>
            </p>
            <div className="pts:space-y-2 pts:text-sm pts:text-blue-700">
              <p>• 初始宽度: 300px</p>
              <p>• 最小宽度: 200px</p>
              <p>• 最大宽度: 500px</p>
              <p>• 高度: 384px</p>
            </div>
            <div className="pts:mt-4 pts:p-3 pts:bg-blue-100 pts:rounded">
              <p className="pts:text-xs pts:text-blue-800">
                拖拽{leftHandlePosition === 'left' ? '左侧' : '右侧'}边缘的手柄来调整宽度
              </p>
            </div>
          </div>
        </ResizableContainer>

        {/* 中间分隔区域 */}
        <div className="pts:flex-1 pts:bg-gray-100 pts:rounded-lg pts:flex pts:items-center pts:justify-center">
          <div className="pts:text-center pts:text-gray-500">
            <p className="pts:text-lg pts:font-medium">中间区域</p>
            <p className="pts:text-sm">自适应宽度</p>
          </div>
        </div>

        {/* 右侧容器 */}
        <ResizableContainer
          height={384}
          initialWidth={300}
          minWidth={200}
          maxWidth={500}
          handlePosition={rightHandlePosition}
          className="pts:bg-green-50"
        >
          <div className="pts:p-4 pts:h-full">
            <h3 className="pts:text-lg pts:font-semibold pts:mb-2 pts:text-green-800">
              右侧容器
            </h3>
            <p className="pts:text-green-600 pts:mb-4">
              手柄位置: <strong>{rightHandlePosition === 'left' ? '左侧' : '右侧'}</strong>
            </p>
            <div className="pts:space-y-2 pts:text-sm pts:text-green-700">
              <p>• 初始宽度: 300px</p>
              <p>• 最小宽度: 200px</p>
              <p>• 最大宽度: 500px</p>
              <p>• 高度: 384px</p>
            </div>
            <div className="pts:mt-4 pts:p-3 pts:bg-green-100 pts:rounded">
              <p className="pts:text-xs pts:text-green-800">
                拖拽{rightHandlePosition === 'left' ? '左侧' : '右侧'}边缘的手柄来调整宽度
              </p>
            </div>
          </div>
        </ResizableContainer>
      </div>

      {/* 使用说明 */}
      <div className="pts:bg-yellow-50 pts:p-4 pts:rounded-lg">
        <h2 className="pts:text-lg pts:font-semibold pts:mb-2 pts:text-yellow-800">使用说明</h2>
        <ul className="pts:space-y-1 pts:text-sm pts:text-yellow-700">
          <li>• 使用控制面板切换每个容器的拖拽手柄位置</li>
          <li>• 拖拽手柄可以位于容器的左侧或右侧边缘</li>
          <li>• 左侧手柄：向右拖拽减少宽度，向左拖拽增加宽度</li>
          <li>• 右侧手柄：向右拖拽增加宽度，向左拖拽减少宽度</li>
          <li>• 所有容器都遵守设定的最小和最大宽度限制</li>
        </ul>
      </div>

      {/* 代码示例 */}
      <div className="pts:bg-gray-50 pts:p-4 pts:rounded-lg">
        <h2 className="pts:text-lg pts:font-semibold pts:mb-2">代码示例</h2>
        <pre className="pts:bg-gray-800 pts:text-green-400 pts:p-4 pts:rounded pts:text-sm pts:overflow-x-auto">
{`<ResizableContainer
  height={400}
  initialWidth={300}
  minWidth={200}
  maxWidth={500}
  handlePosition="left" // 或 "right"
  className="custom-class"
>
  <div>您的内容</div>
</ResizableContainer>`}
        </pre>
      </div>
    </div>
  );
};

export default ResizableContainerDemo;
