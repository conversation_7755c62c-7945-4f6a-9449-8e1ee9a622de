import React, { useState } from 'react';

import { ResizableContainer } from '@cscs-agent/presets';

const ResizableContainerDemo: React.FC = () => {
  // 单列模式状态
  const [leftHandlePosition, setLeftHandlePosition] = useState<'left' | 'right'>('left');
  const [rightHandlePosition, setRightHandlePosition] = useState<'left' | 'right'>('right');

  // 两列模式状态
  const [initialLeftWidth, setInitialLeftWidth] = useState<string>('50%');
  const [minLeftWidth, setMinLeftWidth] = useState<number>(200);
  const [maxLeftWidth, setMaxLeftWidth] = useState<number>(800);

  return (
    <div className="pts:space-y-8 pts:p-8">
      <div className="pts:text-center">
        <h1 className="pts:mb-4 pts:font-bold pts:text-3xl">ResizableContainer 演示</h1>
        <p className="pts:mb-8 pts:text-gray-600">
          展示 ResizableContainer 组件的单列模式和全新的两列布局功能
        </p>
      </div>

      {/* 两列布局演示 */}
      <div className="pts:bg-blue-50 pts:p-6 pts:rounded-lg">
        <h2 className="pts:mb-4 pts:font-semibold pts:text-blue-800 pts:text-xl">🆕 两列布局模式</h2>

        {/* 两列模式控制面板 */}
        <div className="pts:bg-white pts:mb-6 pts:p-4 pts:rounded-lg">
          <h3 className="pts:mb-4 pts:font-medium pts:text-lg">两列模式配置</h3>
          <div className="pts:gap-4 pts:grid pts:grid-cols-3">
            <div>
              <label className="pts:block pts:mb-2 pts:font-medium pts:text-sm">
                初始左列宽度:
              </label>
              <select
                value={initialLeftWidth}
                onChange={(e) => setInitialLeftWidth(e.target.value)}
                className="pts:p-2 pts:border pts:border-gray-300 pts:rounded pts:w-full"
              >
                <option value="30%">30%</option>
                <option value="40%">40%</option>
                <option value="50%">50%</option>
                <option value="60%">60%</option>
                <option value="70%">70%</option>
              </select>
            </div>
            <div>
              <label className="pts:block pts:mb-2 pts:font-medium pts:text-sm">
                最小左列宽度 (px):
              </label>
              <input
                type="number"
                value={minLeftWidth}
                onChange={(e) => setMinLeftWidth(Number(e.target.value))}
                className="pts:p-2 pts:border pts:border-gray-300 pts:rounded pts:w-full"
                min="100"
                max="500"
              />
            </div>
            <div>
              <label className="pts:block pts:mb-2 pts:font-medium pts:text-sm">
                最大左列宽度 (px):
              </label>
              <input
                type="number"
                value={maxLeftWidth}
                onChange={(e) => setMaxLeftWidth(Number(e.target.value))}
                className="pts:p-2 pts:border pts:border-gray-300 pts:rounded pts:w-full"
                min="400"
                max="1200"
              />
            </div>
          </div>
        </div>

        {/* 两列布局演示区域 */}
        <div className="pts:h-96">
          <ResizableContainer
            leftContent={
              <div className="pts:bg-gradient-to-br pts:from-blue-100 pts:to-blue-200 pts:p-4 pts:h-full">
                <h3 className="pts:mb-3 pts:font-semibold pts:text-blue-800 pts:text-lg">
                  左列内容
                </h3>
                <div className="pts:space-y-3 pts:text-blue-700 pts:text-sm">
                  <p>• 这是左列的内容区域</p>
                  <p>• 可以拖拽中间的分隔线调整宽度</p>
                  <p>• 支持键盘导航：Tab 聚焦，方向键调整</p>
                  <p>• 当前配置：</p>
                  <ul className="pts:space-y-1 pts:ml-4">
                    <li>- 初始宽度: {initialLeftWidth}</li>
                    <li>- 最小宽度: {minLeftWidth}px</li>
                    <li>- 最大宽度: {maxLeftWidth}px</li>
                  </ul>
                </div>
                <div className="pts:bg-blue-200 pts:mt-4 pts:p-3 pts:rounded">
                  <p className="pts:text-blue-800 pts:text-xs">
                    💡 提示：使用 Tab 键聚焦分隔线，然后用左右方向键调整宽度
                  </p>
                </div>
              </div>
            }
            rightContent={
              <div className="pts:bg-gradient-to-br pts:from-green-100 pts:to-green-200 pts:p-4 pts:h-full">
                <h3 className="pts:mb-3 pts:font-semibold pts:text-green-800 pts:text-lg">
                  右列内容
                </h3>
                <div className="pts:space-y-3 pts:text-green-700 pts:text-sm">
                  <p>• 这是右列的内容区域</p>
                  <p>• 宽度会自动适应左列的变化</p>
                  <p>• 支持无障碍访问和键盘操作</p>
                  <p>• 键盘快捷键：</p>
                  <ul className="pts:space-y-1 pts:ml-4">
                    <li>- ← → : 调整宽度 (±5%)</li>
                    <li>- Home: 重置到 20%</li>
                    <li>- End: 重置到 80%</li>
                  </ul>
                </div>
                <div className="pts:bg-green-200 pts:mt-4 pts:p-3 pts:rounded">
                  <p className="pts:text-green-800 pts:text-xs">
                    ✨ 特性：平滑过渡动画、视觉反馈、ARIA 无障碍支持
                  </p>
                </div>
              </div>
            }
            initialLeftWidth={initialLeftWidth}
            minLeftWidth={minLeftWidth}
            maxLeftWidth={maxLeftWidth}
            height={384}
            className="pts:shadow-lg"
          />
        </div>
      </div>

      {/* 单列模式演示（向后兼容） */}
      <div className="pts:bg-gray-50 pts:p-6 pts:rounded-lg">
        <h2 className="pts:mb-4 pts:font-semibold pts:text-gray-800 pts:text-xl">📦 单列模式（向后兼容）</h2>

        {/* 单列模式控制面板 */}
        <div className="pts:bg-white pts:mb-6 pts:p-4 pts:rounded-lg">
          <h3 className="pts:mb-4 pts:font-medium pts:text-lg">单列模式配置</h3>
          <div className="pts:gap-4 pts:grid pts:grid-cols-2">
            <div>
              <label className="pts:block pts:mb-2 pts:font-medium pts:text-sm">
                左侧容器手柄位置:
              </label>
              <select
                value={leftHandlePosition}
                onChange={(e) => setLeftHandlePosition(e.target.value as 'left' | 'right')}
                className="pts:p-2 pts:border pts:border-gray-300 pts:rounded pts:w-full"
              >
                <option value="left">左侧</option>
                <option value="right">右侧</option>
              </select>
            </div>
            <div>
              <label className="pts:block pts:mb-2 pts:font-medium pts:text-sm">
                右侧容器手柄位置:
              </label>
              <select
                value={rightHandlePosition}
                onChange={(e) => setRightHandlePosition(e.target.value as 'left' | 'right')}
                className="pts:p-2 pts:border pts:border-gray-300 pts:rounded pts:w-full"
              >
                <option value="left">左侧</option>
                <option value="right">右侧</option>
              </select>
            </div>
          </div>
        </div>

        {/* 单列模式演示区域 */}
        <div className="pts:flex pts:gap-4 pts:h-96">
          {/* 左侧容器 */}
        <ResizableContainer
          height={384}
          initialWidth={300}
          minWidth={200}
          maxWidth={500}
          handlePosition={leftHandlePosition}
          className="pts:bg-blue-50"
        >
          <div className="pts:p-4 pts:h-full">
            <h3 className="pts:mb-2 pts:font-semibold pts:text-blue-800 pts:text-lg">
              左侧容器
            </h3>
            <p className="pts:mb-4 pts:text-blue-600">
              手柄位置: <strong>{leftHandlePosition === 'left' ? '左侧' : '右侧'}</strong>
            </p>
            <div className="pts:space-y-2 pts:text-blue-700 pts:text-sm">
              <p>• 初始宽度: 300px</p>
              <p>• 最小宽度: 200px</p>
              <p>• 最大宽度: 500px</p>
              <p>• 高度: 384px</p>
            </div>
            <div className="pts:bg-blue-100 pts:mt-4 pts:p-3 pts:rounded">
              <p className="pts:text-blue-800 pts:text-xs">
                拖拽{leftHandlePosition === 'left' ? '左侧' : '右侧'}边缘的手柄来调整宽度
              </p>
            </div>
          </div>
        </ResizableContainer>

        {/* 中间分隔区域 */}
        <div className="pts:flex pts:flex-1 pts:justify-center pts:items-center pts:bg-gray-100 pts:rounded-lg">
          <div className="pts:text-gray-500 pts:text-center">
            <p className="pts:font-medium pts:text-lg">中间区域</p>
            <p className="pts:text-sm">自适应宽度</p>
          </div>
        </div>

        {/* 右侧容器 */}
        <ResizableContainer
          height={384}
          initialWidth={300}
          minWidth={200}
          maxWidth={500}
          handlePosition={rightHandlePosition}
          className="pts:bg-green-50"
        >
          <div className="pts:p-4 pts:h-full">
            <h3 className="pts:mb-2 pts:font-semibold pts:text-green-800 pts:text-lg">
              右侧容器
            </h3>
            <p className="pts:mb-4 pts:text-green-600">
              手柄位置: <strong>{rightHandlePosition === 'left' ? '左侧' : '右侧'}</strong>
            </p>
            <div className="pts:space-y-2 pts:text-green-700 pts:text-sm">
              <p>• 初始宽度: 300px</p>
              <p>• 最小宽度: 200px</p>
              <p>• 最大宽度: 500px</p>
              <p>• 高度: 384px</p>
            </div>
            <div className="pts:bg-green-100 pts:mt-4 pts:p-3 pts:rounded">
              <p className="pts:text-green-800 pts:text-xs">
                拖拽{rightHandlePosition === 'left' ? '左侧' : '右侧'}边缘的手柄来调整宽度
              </p>
            </div>
          </div>
        </ResizableContainer>
      </div>
      </div>

      {/* 使用说明 */}
      <div className="pts:bg-yellow-50 pts:p-4 pts:rounded-lg">
        <h2 className="pts:mb-4 pts:font-semibold pts:text-yellow-800 pts:text-lg">📖 使用说明</h2>

        <div className="pts:gap-6 pts:grid md:pts:grid-cols-2 pts:grid-cols-1">
          {/* 两列模式说明 */}
          <div>
            <h3 className="pts:mb-2 pts:font-medium pts:text-yellow-800">🆕 两列布局模式</h3>
            <ul className="pts:space-y-1 pts:text-yellow-700 pts:text-sm">
              <li>• 拖拽中间分隔线调整左右列宽度比例</li>
              <li>• 支持键盘导航：Tab 聚焦分隔线</li>
              <li>• 方向键调整：← → 键调整宽度 (±5%)</li>
              <li>• 快捷键：Home (20%) / End (80%)</li>
              <li>• 自动遵守最小/最大宽度限制</li>
              <li>• 平滑过渡动画和视觉反馈</li>
              <li>• 完整的 ARIA 无障碍支持</li>
            </ul>
          </div>

          {/* 单列模式说明 */}
          <div>
            <h3 className="pts:mb-2 pts:font-medium pts:text-yellow-800">📦 单列模式（向后兼容）</h3>
            <ul className="pts:space-y-1 pts:text-yellow-700 pts:text-sm">
              <li>• 使用控制面板切换拖拽手柄位置</li>
              <li>• 拖拽手柄可以位于左侧或右侧边缘</li>
              <li>• 左侧手柄：向右拖拽减少宽度，向左拖拽增加宽度</li>
              <li>• 右侧手柄：向右拖拽增加宽度，向左拖拽减少宽度</li>
              <li>• 所有容器都遵守设定的最小和最大宽度限制</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 代码示例 */}
      <div className="pts:bg-gray-50 pts:p-4 pts:rounded-lg">
        <h2 className="pts:mb-4 pts:font-semibold pts:text-lg">💻 代码示例</h2>

        <div className="pts:space-y-6">
          {/* 两列模式示例 */}
          <div>
            <h3 className="pts:mb-2 pts:font-medium pts:text-gray-800">🆕 两列布局模式</h3>
            <pre className="pts:bg-gray-800 pts:p-4 pts:rounded pts:overflow-x-auto pts:text-green-400 pts:text-sm">
{`<ResizableContainer
  leftContent={<div>左列内容</div>}
  rightContent={<div>右列内容</div>}
  initialLeftWidth="50%"  // 初始左列宽度
  minLeftWidth={200}      // 最小左列宽度 (px)
  maxLeftWidth={800}      // 最大左列宽度 (px)
  height={400}
  className="custom-class"
/>`}
            </pre>
          </div>

          {/* 向后兼容示例 */}
          <div>
            <h3 className="pts:mb-2 pts:font-medium pts:text-gray-800">📦 单列模式（向后兼容）</h3>
            <pre className="pts:bg-gray-800 pts:p-4 pts:rounded pts:overflow-x-auto pts:text-green-400 pts:text-sm">
{`<ResizableContainer
  height={400}
  initialWidth={300}
  minWidth={200}
  maxWidth={500}
  handlePosition="left" // 或 "right"
  className="custom-class"
>
  <div>您的内容</div>
</ResizableContainer>`}
            </pre>
          </div>

          {/* 向后兼容的两列模式 */}
          <div>
            <h3 className="pts:mb-2 pts:font-medium pts:text-gray-800">🔄 向后兼容的两列模式</h3>
            <pre className="pts:bg-gray-800 pts:p-4 pts:rounded pts:overflow-x-auto pts:text-green-400 pts:text-sm">
{`<ResizableContainer
  leftCol={<div>左列内容</div>}   // 旧属性名
  rightCol={<div>右列内容</div>}  // 旧属性名
  height={400}
/>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResizableContainerDemo;
