import { Select, Space } from "antd";
import React, { useEffect, useState } from "react";

import { BuildInCommand, get, useCommandRunner } from "@cscs-agent/core";

const ConnectionSelect: React.FC = () => {
  const [connections, setConnections] = useState<any[]>([]);
  const [scenarios, setScenarios] = useState<any[]>([]);
  const runner = useCommandRunner();

  useEffect(() => {
    get("/gateway/nl2sql/connections").then((res) => {
      if (Array.isArray(res.data)) {
        const options = res.data.map((i) => ({
          label: (
            <div>
              <div className="text-gray-600">{i.name}</div>
              <div className="text-gray-400">{i.host}</div>
            </div>
          ),
          value: i.id,
        }));
        setConnections(options);
      }
    });
  }, []);

  const handleConnectionChange = (value: string) => {
    get(`/gateway/nl2sql/scenarios/?connection_id=${value}`).then((res) => {
      if (Array.isArray(res.data)) {
        const options = res.data.map((i) => ({
          label: (
            <div>
              <div className="text-gray-600">{i.name}</div>
              <div className="text-gray-400">{i.description}</div>
            </div>
          ),
          value: i.id,
        }));

        setScenarios(options);
      }
    });

    runner(BuildInCommand.UpdateExtendSenderParams, {
      setValue: (prevValue: any) => {
        prevValue.connection_id = value;
        return prevValue;
      },
    });
  };

  const handleScenarioChange = (value: string) => {
    console.log(value);
    runner(BuildInCommand.UpdateExtendSenderParams, {
      setValue: (prevValue: any) => {
        prevValue.scenario_id = value;
        return prevValue;
      },
    });
  };

  return (
    <Space>
      <Select
        options={connections}
        onChange={handleConnectionChange}
        style={{ width: 160 }}
        bordered={false}
        placeholder="请选择数据源"
      />
      <Select
        options={scenarios}
        onChange={handleScenarioChange}
        style={{ width: 250 }}
        bordered={false}
        placeholder="请选择场景"
      />
    </Space>
  );
};

export default ConnectionSelect;
