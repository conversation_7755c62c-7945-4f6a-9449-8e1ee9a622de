import { <PERSON><PERSON>, <PERSON>po<PERSON> } from "antd";
import { message as antdMessage } from "antd";
import React, { PropsWithChildren, useEffect, useRef, useState } from "react";
import { CopyToClipboard } from "react-copy-to-clipboard";

import { Icon } from "@cscs-agent/icons";

// import { renderToString } from "react-dom/server";

const CodeViewer: React.FC<PropsWithChildren<{ code: string; language: string; className: string }>> = (props) => {
  const { language, className, children, ...rest } = props;
  const codeRef = useRef<HTMLDivElement>(null);
  const [text, setText] = useState("");

  useEffect(() => {
    if (codeRef.current) {
      setText(codeRef.current?.textContent ?? "");
    }
  }, [children]);

  // const text = useMemo(() => {
  //   // const htmlString = renderToString(children)
  //   //   .replace(/&quot;/g, '"')
  //   //   .replace(/&lt;/g, "<")
  //   //   .replace(/&gt;/g, ">");
  //   // return htmlString.replace(/<\/?[^>]+(>|$)/g, ""); // Remove HTML tags

  //   return "";
  // }, [children]);

  return language ? (
    <div
      className="ag:mb-4"
      style={{
        boxShadow: "0px 6px 16px 0px rgba(0,0,0,0.08)",
      }}
    >
      <div className="ag:flex ag:justify-between ag:items-center ag:bg-[#EBEBEB] ag:px-4 ag:py-2">
        <span className="ag:text-black-85 ag:text-sm">{language}</span>
        <span className="ag:cursor-pointer">
          <CopyToClipboard
            text={text}
            onCopy={() => {
              antdMessage.success("复制成功");
            }}
          >
            <Button icon={<Icon icon="Copy" />} type="text" size="small">
              <Typography.Text className="ag:text-black-85">复制</Typography.Text>
            </Button>
          </CopyToClipboard>
        </span>
      </div>
      <div className="ag:p-4 ag:overflow-auto ag:whitespace-nowrap" ref={codeRef}>
        <code className={className}>{children}</code>
      </div>
    </div>
  ) : (
    <code className={className} {...rest}>
      {children}
    </code>
  );
};

export default CodeViewer;
