import { But<PERSON> } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { PresetsCommand } from "@cscs-agent/presets";

const OpenSidePanelMessage: React.FC = () => {
  const runner = useCommandRunner();

  const handlePreview = () => {
    runner(BuildInCommand.RenderSidePanel, {
      widgetCode: "@BuildIn/Message",
      widgetProps: {
        url: "/chat/test",
        body: {
          message: "markdown",
          agent_code: "default",
        },
        agentCode: "default",
      },
    });

    runner(BuildInCommand.OpenSidePanel);

    runner(PresetsCommand.CloseSideBar);
  };

  return <Button onClick={handlePreview}>查看报告</Button>;
};

export default OpenSidePanelMessage;
