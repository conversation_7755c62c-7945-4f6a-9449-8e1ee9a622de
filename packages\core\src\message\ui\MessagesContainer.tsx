import { nanoid } from "nanoid";
import React, { useEffect, useRef, useState } from "react";

import { useCommandRunner } from "@/command";
import { useActiveAgentCode, useActiveConversationId, useConversationState, useMessages } from "@/core";
import { Message, createMessage } from "@/core/common/message";
import { cancelRequest, get } from "@/request";
import { BuildInCommand, ConversationData, MessageStatus, Role, StandardResponse } from "@/types";
import { UserOutlined } from "@ant-design/icons";

import MessageComponent from "./Message";

const MessageContainer = () => {
  const [messages, setMessages] = useMessages();
  const [activeConversationId] = useActiveConversationId();
  const [loading, setLoading] = useState(false);
  const [, setActiveAgentCode] = useActiveAgentCode();
  const runner = useCommandRunner();
  const messageReqId = useRef<string | null>(null);
  const conversationReqId = useRef<string | null>(null);
  const { state, setStateByKey } = useConversationState();
  const { isNew } = state ?? {};

  useEffect(() => {
    if (messageReqId.current) {
      cancelRequest(messageReqId.current);
    }
    if (conversationReqId.current) {
      cancelRequest(conversationReqId.current);
    }

    if (activeConversationId) {
      if (!isNew) {
        setLoading(true);
        setMessages([]);
      }
      messageReqId.current = nanoid();
      get("/message", {
        conversation_id: activeConversationId,
        requestId: messageReqId.current,
      })
        .then((res: any) => {
          // TODO any类型
          const list = res.data.data;
          if (Array.isArray(list)) {
            const historyMessages = list
              .map((i) =>
                createMessage({
                  id: i.id,
                  role: i.message_type,
                  content: i.content,
                  status: MessageStatus.Finished,
                  agentCode: i.agent_code,
                  userRating: i.user_rating,
                  extendData: i.data_object ?? {},
                  hidden: i.hidden,
                }),
              )
              .filter(Boolean) as Message[];
            // TODO: 优化加载逻辑
            setMessages((messages) => {
              const lastMessage = messages[messages.length - 1];
              if (messages.length === 0 || lastMessage.status !== MessageStatus.Loading) {
                return historyMessages;
              } else {
                return [...messages];
              }
            });
            loadConversationDetail();
          }
        })
        .catch((error) => {
          if (error.status !== 404) {
            setMessages([]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [activeConversationId]);

  const loadConversationDetail = () => {
    conversationReqId.current = nanoid();
    get<StandardResponse<ConversationData>>(`/conversation/${activeConversationId}`, {
      requestId: conversationReqId.current,
    }).then((res) => {
      const data = res.data.data;
      setActiveAgentCode(data.current_agent_code);

      if (data.task_id && activeConversationId && !isNew && data.current_agent_code) {
        setTimeout(() => {
          loadUnfinishedMessage(activeConversationId, data.current_agent_code);
        });
      }

      // 设置 isNew = false
      setStateByKey("isNew", false);
    });
  };

  const loadUnfinishedMessage = (conversationId: string, agentCode: string) => {
    runner(BuildInCommand.LoadUnfinishedMessage, {
      message: "",
      agentCode: agentCode,
      conversationId: conversationId,
      isNewConversation: false,
    });
  };

  return (
    <>
      {loading && (
        <div className="ag:flex ag:items-center">
          <div className="ag:animate-spin ag:circle-loader"></div>
        </div>
      )}
      <div className="ag:flex ag:flex-col ag:items-stretch ag:gap-4">
        {messages.map((item) => (
          <MessageComponent
            key={item.id}
            message={item}
            avatar={{ icon: <UserOutlined />, style: { background: item.role === Role.HUMAN ? "#87d068" : "#3399ff" } }}
            placement={item.role === Role.HUMAN ? "end" : "start"}
            role={item.role}
          />
        ))}
      </div>
    </>
  );
};

export default MessageContainer;
