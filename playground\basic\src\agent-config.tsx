import React from "react";
import { But<PERSON> } from "antd";
import { dynamicPageConfigFactory, dynamicPageQaAgentConfig, nl2sqlAgentConfig } from "@cscs-agent/agents";
import { Role, type AgentChatConfig } from "@cscs-agent/core";
import {
  Copy,
  PromptTemplate,
  Rating,
  ThoughtChain,
  Message,
  Regenerate,
  FileList,
  UploadButton,
  RAGFileList,
  InternetSearch,
  KnowledgeBaseSelect,
} from "@cscs-agent/presets";

import InsertTag from "./widgets/insert-tag";
import InsertText from "./widgets/insert-text";
import FormDemo from "./widgets/form";
import OpenSidePanelMessage from "./widgets/open-side-panel-message";
import AddExtendData from "./widgets/extend-data";
import AppendWidget from "./widgets/append-widget";
import ConnectionSelect from "./widgets/connection/Connection";

const dynamicPageAgentConfig = dynamicPageConfigFactory({
  saveApiUrl: () => {
    return `/gateway/cluster/page/system/dynamic/page/manage/savePageForAi`;
  },
  previewUrl: (id: string) => `http://172.17.6.116:8002/dynamic_page/agent_preview/${id}`,
});

export const config: AgentChatConfig = {
  agents: [
    dynamicPageAgentConfig,
    dynamicPageQaAgentConfig,
    nl2sqlAgentConfig,
    {
      name: "安全漏洞助手",
      code: "security",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
              props: {
                styles: {
                  itemContainer: {
                    maxHeight: "100px",
                    overflowY: "auto",
                  },
                },
              },
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
            {
              code: "@BuildIn/Message",
              component: Message,
            },
            {
              code: "@Test/OpenSidePanelMessage",
              component: OpenSidePanelMessage,
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [
              {
                code: "AppendWidget",
                component: AppendWidget,
                role: Role.HUMAN,
              },
            ],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "FileList",
                component: FileList,
                placement: "inside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "UploadButton",
                component: UploadButton,
                placement: "inside",
                props: {
                  accept: [
                    ".doc",
                    ".docx",
                    ".ppt",
                    ".pptx",
                    ".xls",
                    ".xlsx",
                    ".pdf",
                    ".md",
                    ".txt",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".png",
                    ".sql",
                  ],
                },
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
    {
      name: "测试智能体",
      code: "test",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
              props: {
                styles: {
                  itemContainer: {
                    maxHeight: "100px",
                    overflowY: "auto",
                  },
                },
              },
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
            {
              code: "@BuildIn/Message",
              component: Message,
            },
            {
              code: "@Test/OpenSidePanelMessage",
              component: OpenSidePanelMessage,
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          header: {
            widgets: [
              {
                code: "@DynamicPage/PreviewButton",
                component: () => {
                  return <Button>code === test</Button>;
                },
                placement: "outside",
                hidden: (message) => message.agentCode !== "test",
              },
              {
                code: "@DynamicPage/PreviewButton",
                component: () => {
                  return <Button>PreviewWidget</Button>;
                },
                placement: "outside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [
              {
                code: "AppendWidget",
                component: AppendWidget,
                role: Role.HUMAN,
                hidden: (message) => message.agentCode !== "test",
              },
            ],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "FileList",
                component: FileList,
                placement: "inside",
              },
              {
                code: "InsertText",
                component: InsertText,
                placement: "outside",
              },
              {
                code: "InsertTag",
                component: InsertTag,
                placement: "outside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "KnowledgeBaseSelect",
                component: KnowledgeBaseSelect,
                placement: "inside",
              },
              {
                code: "InternetSearch",
                component: InternetSearch,
                placement: "inside",
              },
              {
                code: "UploadButton",
                component: UploadButton,
                placement: "inside",
                props: {
                  accept: [
                    ".doc",
                    ".docx",
                    ".ppt",
                    ".pptx",
                    ".xls",
                    ".xlsx",
                    ".pdf",
                    ".md",
                    ".txt",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".png",
                    ".sql",
                  ],
                },
              },
              {
                code: "AddExtendData",
                component: AddExtendData,
                placement: "inside",
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            {
              code: "@BuildIn/Message",
              component: Message,
            },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
    {
      name: "CM测试智能体",
      code: "cm-report-agent",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
              props: {
                styles: {
                  itemContainer: {
                    maxHeight: "100px",
                    overflowY: "auto",
                  },
                },
              },
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
            {
              code: "@BuildIn/Message",
              component: Message,
            },
            {
              code: "@Test/OpenSidePanelMessage",
              component: OpenSidePanelMessage,
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [
              {
                code: "AppendWidget",
                component: AppendWidget,
                role: Role.HUMAN,
              },
            ],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "FileList",
                component: FileList,
                placement: "inside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "UploadButton",
                component: UploadButton,
                placement: "inside",
                props: {
                  accept: [
                    ".doc",
                    ".docx",
                    ".ppt",
                    ".pptx",
                    ".xls",
                    ".xlsx",
                    ".pdf",
                    ".md",
                    ".txt",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".png",
                    ".sql",
                  ],
                },
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
    {
      name: "数据分析平台",
      code: "data_analysis",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
              props: {
                styles: {
                  itemContainer: {
                    maxHeight: "100px",
                    overflowY: "auto",
                  },
                },
              },
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [],
          },
          header: {
            widgets: [],
          },
          footer: {
            widgets: [
              {
                code: "ConnectionSelect",
                component: ConnectionSelect,
                placement: "inside",
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
    {
      name: "ABS报告分析智能体",
      code: "abs-report-analyzer",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            {
              code: "@BuildIn/ThoughtChain",
              component: ThoughtChain,
              props: {
                styles: {
                  itemContainer: {
                    maxHeight: "100px",
                    overflowY: "auto",
                  },
                },
              },
            },
            {
              code: "@Test/FormDemo",
              component: FormDemo,
            },
            {
              code: "@BuildIn/Message",
              component: Message,
            },
            {
              code: "@Test/OpenSidePanelMessage",
              component: OpenSidePanelMessage,
            },
            {
              code: "@BuildIn/RAGFileList",
              component: RAGFileList,
            },
          ],
        },
        slots: {
          footer: {
            widgets: [
              {
                code: "Copy",
                component: Copy,
                role: Role.AI,
              },
              {
                code: "Rating",
                component: Rating,
                role: Role.AI,
              },
              {
                code: "Regenerate",
                component: Regenerate,
                role: Role.AI,
              },
            ],
          },
          append: {
            widgets: [
              {
                code: "AppendWidget",
                component: AppendWidget,
                role: Role.HUMAN,
              },
            ],
          },
        },
      },
      prompts: [],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              {
                code: "PromptTemplate",
                component: PromptTemplate,
              },
            ],
          },
          header: {
            widgets: [
              {
                code: "FileList",
                component: FileList,
                placement: "inside",
              },
            ],
          },
          footer: {
            widgets: [
              {
                code: "UploadButton",
                component: UploadButton,
                placement: "inside",
                props: {
                  accept: [
                    ".doc",
                    ".docx",
                    ".ppt",
                    ".pptx",
                    ".xls",
                    ".xlsx",
                    ".pdf",
                    ".md",
                    ".txt",
                    ".jpg",
                    ".jpeg",
                    ".gif",
                    ".png",
                    ".sql",
                  ],
                },
              },
            ],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [],
        },
      },
      request: {
        chat: {
          url: "/chat/completion",
          headers: {},
          method: "POST",
        },
      },
    },
  ],
  filterConversation: true,
};
