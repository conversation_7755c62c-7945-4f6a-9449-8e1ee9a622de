import { useThrottleFn } from "ahooks";
import React, { useEffect, useRef } from "react";

import { MessageRender } from "../../components";

interface MessageProps {
  url: string;
  method?: string;
  headers?: Record<string, string>;
  body?: Record<string, string>;
  params?: Record<string, string>;
  message?: string;
  agentCode: string;
}

const Message: React.FC<MessageProps> = (props) => {
  const { url, method, headers, body, params, message, agentCode } = props;
  const renderRef = useRef<any>(null);
  const scrollWrapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (url) {
      renderRef.current?.runReq();
    }
  }, [url]);

  const { run: handleMessageChange } = useThrottleFn(
    () => {
      scrollWrapRef.current?.scrollTo({
        top: scrollWrapRef.current.scrollHeight,
        behavior: "smooth",
      });
    },
    { wait: 500 },
  );

  return (
    <div ref={scrollWrapRef} className="pts:p-4 pts:h-full pts:overflow-y-auto">
      <MessageRender
        ref={renderRef}
        message={message}
        agentCode={agentCode}
        requestOptions={{
          url,
          method,
          headers,
          body,
          params,
        }}
        onMessageChange={handleMessageChange}
      />
    </div>
  );
};

export default Message;
