import { useThrottleFn } from "ahooks";
import React, { useEffect, useRef, useState } from "react";

import { BuildInCommand, useCurrentAgentAccessible, useMessages, useSubscribeCommand } from "@cscs-agent/core";

import { ResizableContainer } from "../components";

interface DefaultChatLayoutProps {
  navigationBar?: React.ReactNode;
  message: React.ReactNode;
  sender: React.ReactNode;
  sidePanel: React.ReactNode;
}

const DefaultChatLayout: React.FC<DefaultChatLayoutProps> = (props) => {
  const { navigationBar, message, sender, sidePanel } = props;
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const scrollWrapRef = useRef<HTMLDivElement>(null);
  const [sidePanelIsOpen, setSidePanelIsOpen] = React.useState(false);
  const [disableAutoScroll, setDisableAutoScroll] = React.useState(false);
  const [messages] = useMessages();
  const accessible = useCurrentAgentAccessible();

  // 自动滚动到底部
  const { run: throttleScrollToBottom } = useThrottleFn(
    () => {
      if (disableAutoScroll) return;
      if (scrollWrapRef.current) {
        scrollWrapRef.current.scrollTo({
          top: scrollWrapRef.current.scrollHeight,
          behavior: "smooth",
        });
      }
    },
    { wait: 500 },
  );

  useEffect(() => {
    // 监听消息容器高度变化
    const messageContainer = messageContainerRef.current;
    if (!messageContainer) return;

    const resizeObserver = new ResizeObserver(() => {
      throttleScrollToBottom();
    });
    resizeObserver.observe(messageContainer);

    return () => {
      resizeObserver.disconnect();
    };
  }, [throttleScrollToBottom]);

  useEffect(() => {
    // 每当消息变化时，开启自动滚动
    setDisableAutoScroll(false);
  }, [messages]);

  useSubscribeCommand(BuildInCommand.OpenSidePanel, () => {
    setSidePanelIsOpen(true);
  });

  useSubscribeCommand(BuildInCommand.CloseSidePanel, () => {
    setSidePanelIsOpen(false);
  });

  return (
    <div className={` pts:h-full pts:w-full ${sidePanelIsOpen ? "pts:bg-[#f8f9fb]" : "pts:bg-white"}`}>
      <ResizableContainer
        minRightWidth="400px"
        minLeftWidth="400px"
        disabled={!sidePanelIsOpen}
        leftContent={
          <div className={`pts:h-full pts:flex pts:flex-col ${sidePanelIsOpen ? "" : "pts:flex-1"}`}>
            <div>{navigationBar}</div>
            <div
              ref={scrollWrapRef}
              className="pts:flex-1 pts:p-4 pts:px-6 pts:w-full pts:overflow-y-scroll mini-scrollbar"
              onWheel={() => {
                setDisableAutoScroll(true);
              }}
            >
              <div ref={messageContainerRef} className="pts:mx-auto pts:max-w-[800px] pts:translate-x-[3px]">
                {message}
              </div>
            </div>
            <div className="pts:mx-auto pts:px-6 pts:pb-2 pts:w-full pts:max-w-[848px]" hidden={!accessible}>
              {sender}
            </div>
            <div className="pts:pb-2 pts:text-black-45 pts:text-sm pts:text-center">内容由 AI 生成，请仔细甄别</div>
          </div>
        }
        rightContent={sidePanel}
        hideRightContent={!sidePanelIsOpen}
      />
    </div>
  );
};

export default DefaultChatLayout;
