import { Tooltip } from "antd";
import React from "react";

import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

const InternetSearch: React.FC = () => {
  const runner = useCommandRunner();
  const [enable, setEnable] = React.useState(false);

  const handleChange = () => {
    setEnable(!enable);
    runner(BuildInCommand.UpdateExtendSenderParams, {
      setValue: (prevValue: any) => {
        prevValue.enable_internet_search = !enable;
        return prevValue;
      },
    });
  };

  return (
    <Tooltip title="联网搜索">
      <div
        className="pts:relative pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:rounded-lg pts:w-8 pts:h-8 pts:cursor-pointer"
        onClick={handleChange}
      >
        <Icon icon="Internet" className={enable ? "pts:text-primary" : ""} />
      </div>
    </Tooltip>
  );
};

export default InternetSearch;
