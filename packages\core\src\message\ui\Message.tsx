import { Alert } from "antd";
import React, { useState } from "react";

import { useSubscribeCommand } from "@/command";
import { Message } from "@/core/common/message";
import { BuildInCommand, IUpdateMessageStateParams, MessageStatus, Role } from "@/types";

import { MessageRender } from "../render";
import Append from "./Append";
import MessageContext from "./Context";
import Footer from "./Footer";
import Header from "./Header";

interface MessageComponentProps {
  message: Message;
  messageRender?: (content: string) => React.ReactNode;
  avatar: {
    icon: React.ReactNode;
    style?: React.CSSProperties;
  };
  placement: "start" | "end";
  role: Role;
}

const MessageComponent: React.FC<MessageComponentProps> = (props) => {
  const { message, placement, role } = props;
  const [messageState, setMessageState] = useState<Record<string, any>>({});

  // 更新Message State
  useSubscribeCommand(BuildInCommand.UpdateMessageState, (params: IUpdateMessageStateParams) => {
    const { messageId, setValue } = params;
    if (messageId === message.id && setValue) {
      const newValue = setValue(messageState);
      setMessageState(newValue ?? {});
    }
  });

  return (
    <MessageContext.Provider value={{ message, messageState, setMessageState }}>
      <div className={`ag:w-full ag:flex ${placement === "end" ? "ag:justify-end" : ""}`} hidden={message.hidden}>
        <div className={`${placement === "end" ? "ag:max-w-[540px]" : "ag:w-[100%]"} cscs-agent-bubble`}>
          <div className="ag:pb-2">
            <Header placement="outside" />
          </div>
          <div className={`${placement === "end" ? "ag:py-2 ag:bg-[rgba(37,45,62,0.06)] ag:p-3 ag:rounded-lg" : ""}`}>
            <Header placement="inside" />
            <MessageRender data={message} />
            {message.status === MessageStatus.Loading && <div className="ag:mt-4 ag:message-loader"></div>}
            <Append role={role} placement="inside" />
          </div>
          <div className="ag:pt-2">
            <Append role={role} placement="outside" />
          </div>
          {message.error && (
            <div className="ag:mt-2 ag:max">
              <Alert type="error" message={message.getErrorMessage()} showIcon />
            </div>
          )}
          {message.status === MessageStatus.Cancelled && (
            <div className="ag:mt-2 ag:max">
              <span className="ag:text-black-45 ag:text-sm">已停止生成</span>
            </div>
          )}

          <div
            className={`ag:flex ag:mt-2 ${role === Role.HUMAN ? "ag:flex-row-reverse cscs-agent-bubble-footer-human" : ""}`}
          >
            <Footer role={role} />
          </div>
        </div>
      </div>
    </MessageContext.Provider>
  );
};

export default MessageComponent;
