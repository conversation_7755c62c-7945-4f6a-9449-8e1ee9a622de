import React, { useEffect, useMemo } from "react";

import { DefaultChatLayout } from "@/layout";
import {
  BuildInCommand,
  MessageContainer,
  Sender,
  SidePanel,
  useActiveConversationId,
  useCommandRunner,
  useMessages,
} from "@cscs-agent/core";

const ChatEmbedded: React.FC<{ id?: string }> = (props) => {
  const { id } = props;
  const [, setActiveConversationId] = useActiveConversationId();
  const runner = useCommandRunner();
  const [, setMessages] = useMessages();

  useEffect(() => {
    setActiveConversationId(id ?? null);

    return () => {
      setActiveConversationId(null);
      setMessages([]);
      runner(BuildInCommand.CancelChatRequest);
    };
  }, [id]);

  const senderMemo = useMemo(() => {
    return <Sender />;
  }, []);

  const messageContainer = useMemo(() => <MessageContainer />, []);

  return (
    <DefaultChatLayout
      message={messageContainer}
      sender={senderMemo}
      sidePanel={
        <div className="pts:bg-gray-100" style={{ height: "100vh" }}>
          <SidePanel />
        </div>
      }
    />
  );
};

export default ChatEmbedded;
