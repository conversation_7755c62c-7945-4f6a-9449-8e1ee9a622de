import { Button, Input, Modal, <PERSON>confirm, <PERSON>, Tooltip, message as antdMessage } from "antd";
import React from "react";

import { DislikeFilled, DislikeOutlined, LikeFilled, LikeOutlined } from "@ant-design/icons";
import { MessageContext, MessageStatus, post } from "@cscs-agent/core";

const Rating: React.FC = () => {
  const context = React.useContext(MessageContext);
  const [rating, setRating] = React.useState<"like" | "dislike" | null>(context?.message.userRating ?? null);
  const [dislikeContent, setDislikeContent] = React.useState<string>("");
  const [dislikeModalVisible, setDislikeModalVisible] = React.useState<boolean>(false);
  const [tempDislikeContent, setTempDislikeContent] = React.useState<string>("");
  const status = context?.message.status;

  const handleRating = ($rating: "like" | "dislike") => {
    if (!context?.message) return;

    // message loading 状态不允许操作
    if (context?.message.status === MessageStatus.Loading) {
      antdMessage.warning("请等待消息加载完成");
      return;
    }

    const message = context.message;

    if ($rating === "like") {
      if (rating === "like") {
        // 取消点赞
        post(`/message/${message.id}/rate`, {
          action: "unselect",
          rating: "like",
        }).then(() => {
          setRating(null);
        });
      } else {
        // 点赞
        post(`/message/${message.id}/rate`, {
          action: "select",
          rating: "like",
        }).then(() => {
          setRating("like");
        });
      }
    } else if ($rating === "dislike") {
      if (rating === "dislike") {
        // 取消点踩
        post(`/message/${message.id}/rate`, {
          action: "unselect",
          rating: "dislike",
        }).then(() => {
          setRating(null);
          setDislikeContent("");
        });
      } else {
        // 不喜欢打开弹窗
        setTempDislikeContent(dislikeContent);
        setDislikeModalVisible(true);
        setTempDislikeContent("");
      }
    }
  };

  const handleDislikeSubmit = () => {
    if (!context?.message) return;

    const message = context.message;
    post(`/message/${message.id}/rate`, {
      action: "select",
      rating: "dislike",
      dislike_content: tempDislikeContent,
    }).then(() => {
      setRating("dislike");
      setDislikeContent(tempDislikeContent);
      setDislikeModalVisible(false);
    });
  };

  const handleDislikeCancel = () => {
    setDislikeModalVisible(false);
    setTempDislikeContent(dislikeContent);
    setTempDislikeContent("");
  };

  const isInputEmpty = tempDislikeContent.trim().length === 0;

  if (status === MessageStatus.Cancelled) {
    return null;
  }

  return (
    <>
      <Space>
        <Tooltip title="喜欢">
          {rating === "like" ? (
            <Button
              onClick={() => handleRating("like")}
              type="text"
              size="small"
              icon={
                <span className="pts:text-black-65">
                  <LikeFilled />
                </span>
              }
            ></Button>
          ) : (
            <Button
              type="text"
              size="small"
              icon={
                <span className="pts:text-black-65">
                  <LikeOutlined />
                </span>
              }
              onClick={() => handleRating("like")}
            ></Button>
          )}
        </Tooltip>
        <Tooltip title="不喜欢">
          {rating === "dislike" ? (
            <Popconfirm
              title="你确定要取消点踩吗？取消后，已填写的反馈意见将失效"
              onConfirm={() => handleRating("dislike")}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                size="small"
                icon={
                  <span className="pts:text-black-65">
                    <DislikeFilled />
                  </span>
                }
              ></Button>
            </Popconfirm>
          ) : (
            <Button
              type="text"
              size="small"
              icon={
                <span className="pts:text-black-65">
                  <DislikeOutlined />
                </span>
              }
              onClick={() => handleRating("dislike")}
            ></Button>
          )}
        </Tooltip>
      </Space>

      <Modal
        title="反馈"
        open={dislikeModalVisible}
        onOk={handleDislikeSubmit}
        onCancel={handleDislikeCancel}
        okText="提交"
        cancelText="取消"
        okButtonProps={{ disabled: isInputEmpty }}
        destroyOnClose
      >
        <Input.TextArea
          placeholder="我们想知道你对此回答不满意的原因，你认为更好的回答是什么?"
          value={tempDislikeContent}
          onChange={(e) => setTempDislikeContent(e.target.value)}
          rows={4}
        />
      </Modal>
    </>
  );
};

export default Rating;
