version: '3.8'
name: agent
services:
  agent-server:
    image: agent-server/latest
    container_name: agent-server
    pull_policy: never
    networks:
      - agent-network
    environment:
      - APP_ENV=prod
      - CONFIG_PATH=/app/config/application.yml
    volumes:
      - "/d/Data/docker/data/agent/logs:/app/logs"
      - "/d/Data/docker/data/agent/config:/app/config"
    extra_hosts:
      - "host.docker.internal:host-gateway"

  agent-web:
    image: agent-web/latest
    container_name: agent-web
    pull_policy: never
    ports:
      - "3000:80"
    depends_on:
      - agent-server
    networks:
      - agent-network

networks:
  agent-network:
    driver: bridge