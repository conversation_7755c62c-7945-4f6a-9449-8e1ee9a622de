import { existsSync, mkdirSync, readFileSync, watch, writeFileSync } from "node:fs";
import { tmpdir } from "node:os";
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import { pathToFileURL } from "node:url";
import { InlineConfig } from "vite";

import type { BuilderConfig } from "../types.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

interface BuilderConfigModule {
  default?: BuilderConfig;
  config?: BuilderConfig;
}

// Cache for compiled builder configurations
const builderConfigCache = new Map<string, { config: BuilderConfig; timestamp: number }>();

export class BuilderConfigManager {
  private root: string;

  constructor(root: string) {
    this.root = root;
  }

  /**
   * 从 builder.config.js/ts文件加载配置
   * 配置文件类型为 BuilderConfig
   *
   * 搜索位置的顺序：
   * 1. {root}/builder.config.ts
   * 2. {root}/builder.config.js
   * 3. {cwd}/builder.config.ts
   * 4. {cwd}/builder.config.js
   *
   * @param configPath - 可选的配置文件路径
   * @param useCache - 是否使用缓存结果 (默认: true)
   * @returns Promise<BuilderConfig | null> - BuilderConfig 配置对象或 null（如果未找到）
   */
  async loadConfig(configPath?: string, useCache: boolean = true): Promise<BuilderConfig | null> {
    try {
      // 确定 builder.config 文件路径
      const builderConfigPath = configPath || resolve(this.root, "builder.config.ts");

      // 检查配置文件是否存在
      if (!existsSync(builderConfigPath)) {
        // 尝试其他可能的文件名和位置
        const alternativePaths = [
          resolve(this.root, "builder.config.js"),
          resolve(process.cwd(), "builder.config.ts"),
          resolve(process.cwd(), "builder.config.js"),
        ];

        let foundPath: string | null = null;
        for (const altPath of alternativePaths) {
          if (existsSync(altPath)) {
            foundPath = altPath;
            break;
          }
        }

        if (!foundPath) {
          console.warn(
            `Builder configuration file not found. Searched paths: ${[builderConfigPath, ...alternativePaths].join(", ")}`,
          );
          return null;
        }

        return this.loadAndCompileConfig(foundPath, useCache);
      }

      return this.loadAndCompileConfig(builderConfigPath, useCache);
    } catch (error) {
      console.error("Error loading builder configuration:", error);
      return null;
    }
  }

  /**
   * 从 BuilderConfig 中提取 Vite 配置
   * @param config BuilderConfig 配置对象
   * @returns Partial<UserConfig> - 提取的 Vite 配置
   */
  async getViteConfig(config: BuilderConfig) {
    if (!config.vite) {
      return {};
    }

    const viteConfig: InlineConfig = {};

    // 处理 base 配置
    if (config.vite.base !== undefined) {
      viteConfig.base = config.vite.base;
    }

    // 处理 alias 配置
    if (config.vite.alias) {
      viteConfig.resolve = {
        alias: config.vite.alias,
      };
    }

    // 处理 plugins 配置
    if (config.vite.plugins) {
      const { plugins: pluginConfig } = config.vite;

      if (pluginConfig.plugins && Array.isArray(pluginConfig.plugins)) {
        // 根据模式决定如何处理插件
        if (pluginConfig.mode === "override") {
          // 覆盖模式：完全使用配置中的插件
          viteConfig.plugins = pluginConfig.plugins;
        } else {
          // 追加模式（默认）：将插件添加到现有插件列表中
          // 注意：这里只返回插件，实际的合并逻辑在调用方处理
          viteConfig.plugins = pluginConfig.plugins;
        }
      }
    }

    return viteConfig;
  }

  /**
   * 监听 builder config 文件变化
   * @param path 配置文件路径
   * @param onChange 文件变化时的回调函数
   */
  watchConfigFile(path: string, onChange?: () => void) {
    const watcher = watch(path, async () => {
      console.log("Builder config changed, please restart dev server manually.");
      watcher.close();
      if (onChange) {
        await onChange();
      }
    });
  }

  /**
   * Load and compile TypeScript/JavaScript builder configuration file
   * @param filePath - Path to the builder.config.ts/js file
   * @param useCache - Whether to use cached result
   * @returns Promise<BuilderConfig | null> - Compiled builder configuration
   */
  private async loadAndCompileConfig(filePath: string, useCache: boolean): Promise<BuilderConfig | null> {
    this.watchConfigFile(filePath, () => {
      // 这里可以传递重启回调，但为了解耦，我们暂时只是提示手动重启
    });

    try {
      // Check cache first
      if (useCache && builderConfigCache.has(filePath)) {
        const cached = builderConfigCache.get(filePath)!;
        const fileStats = await import("node:fs/promises").then((fs) => fs.stat(filePath));

        // Check if cached version is still valid (file hasn't been modified)
        if (cached.timestamp >= fileStats.mtimeMs) {
          return cached.config;
        }
      }

      // Read the configuration file content
      const fileContent = readFileSync(filePath, "utf-8");

      // Determine if it's TypeScript or JavaScript and compile accordingly
      const isTypeScript = filePath.endsWith(".ts");
      let compiledJs: string;

      if (isTypeScript) {
        // Compile TypeScript to JavaScript using esbuild
        compiledJs = await this.compileTypeScript(fileContent, filePath);
      } else {
        // For JavaScript files, use content as-is
        compiledJs = fileContent;
      }

      // Create a temporary file to load the compiled JavaScript
      const tempDir = tmpdir();
      const tempFileName = `builder-config-${Date.now()}-${Math.random().toString(36).substring(2, 11)}.mjs`;
      const tempFilePath = resolve(tempDir, tempFileName);

      // Ensure temp directory exists
      if (!existsSync(tempDir)) {
        mkdirSync(tempDir, { recursive: true });
      }

      // Write compiled JavaScript to temporary file
      writeFileSync(tempFilePath, compiledJs, "utf-8");

      try {
        // Dynamically import the compiled module
        const moduleUrl = pathToFileURL(tempFilePath).href;
        const module = (await import(moduleUrl)) as BuilderConfigModule;

        // Extract builder configuration from module
        const config = module.default || module.config;

        if (!config) {
          throw new Error(
            "Builder configuration file must export a default configuration object or named 'config' export",
          );
        }

        // Validate configuration structure
        this.validateConfig(config);

        // Cache the result
        if (useCache) {
          const fileStats = await import("node:fs/promises").then((fs) => fs.stat(filePath));
          builderConfigCache.set(filePath, {
            config,
            timestamp: fileStats.mtimeMs,
          });
        }

        return config;
      } finally {
        // Clean up temporary file
        try {
          await import("node:fs/promises").then((fs) => fs.unlink(tempFilePath));
        } catch (cleanupError) {
          console.warn("Failed to clean up temporary file:", tempFilePath, cleanupError);
        }
      }
    } catch (error) {
      console.error("Error compiling builder configuration:", error);
      throw error;
    }
  }

  /**
   * Compile TypeScript content to JavaScript using esbuild
   * @param tsContent - TypeScript source code
   * @param filePath - Original file path (for error reporting)
   * @returns Promise<string> - Compiled JavaScript code
   */
  private async compileTypeScript(tsContent: string, filePath: string): Promise<string> {
    try {
      // Use esbuild to compile TypeScript to JavaScript
      const esbuild = await import("esbuild");

      const result = await esbuild.transform(tsContent, {
        loader: "ts",
        target: "es2022",
        format: "esm",
        sourcefile: filePath,
        keepNames: true,
        minify: false,
        sourcemap: false,
      });

      if (result.warnings.length > 0) {
        console.warn("TypeScript compilation warnings:", result.warnings);
      }

      return result.code;
    } catch (error) {
      console.error("TypeScript compilation failed:", error);
      throw new Error(
        `Failed to compile TypeScript builder configuration: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Validate builder configuration structure
   * @param config - Builder configuration object to validate
   */
  private validateConfig(config: BuilderConfig): void {
    if (!config || typeof config !== "object") {
      throw new Error("Builder configuration must be an object");
    }

    // vite is optional, but if present, validate its structure
    if (config.vite !== undefined) {
      if (!config.vite || typeof config.vite !== "object") {
        throw new Error("Builder configuration vite must be an object if defined");
      }

      const { vite } = config;

      // Validate vite.base (optional)
      if (vite.base !== undefined && typeof vite.base !== "string") {
        throw new Error("Builder configuration vite.base must be a string");
      }

      // Validate vite.alias (optional)
      if (vite.alias !== undefined) {
        if (!vite.alias || typeof vite.alias !== "object") {
          throw new Error("Builder configuration vite.alias must be an object");
        }
        for (const [alias, path] of Object.entries(vite.alias)) {
          if (typeof path !== "string") {
            throw new Error(`Builder configuration vite.alias.${alias} must be a string`);
          }
        }
      }

      // Validate vite.plugins (optional)
      if (vite.plugins !== undefined) {
        if (!vite.plugins || typeof vite.plugins !== "object") {
          throw new Error("Builder configuration vite.plugins must be an object");
        }

        // Validate vite.plugins.mode (optional)
        if (vite.plugins.mode !== undefined && !["append", "override"].includes(vite.plugins.mode)) {
          throw new Error("Builder configuration vite.plugins.mode must be 'append' or 'override'");
        }

        // Validate vite.plugins.plugins (optional)
        if (vite.plugins.plugins !== undefined && !Array.isArray(vite.plugins.plugins)) {
          throw new Error("Builder configuration vite.plugins.plugins must be an array");
        }
      }
    }
  }
}
