import { defineMock } from "vite-plugin-mock-dev-server";

import { MessageMocker } from "@cscs-agent/mock";

export default defineMock([
  {
    url: "x/api/chat/test",
    method: "POST",
    response: (req, res) => {
      const message = `
data: {"event_id": 0, "event_type": 1000, "package_id": 0, "package_type": 1, "chunk_id": 0, "is_last": true, "data": "{\\"conversation_id\\": \\"15868054b0924f37836e40de53e5ce9a\\", \\"message_id\\": \\"7f2f89f873de4fd4b0aa80ba0c949b01\\", \\"type\\": \\"header\\"}"}

data: {"event_id": 1, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 0, "is_last": false, "data": "<message-embedded>\\n                            <state>\\n                                <set>\\n                                    <strategy>replace</strategy>\\n                                    <path>search_company_by_keywords</path>\\n                                    <value>loading</value>\\n                                </set>\\n                                <set>\\n                                    <strategy>replace</strategy>\\n                                    <path>search_company_by_keywords_result</path>\\n                                    <value></value>\\n                                </set>\\n                            </state>\\n                            <widget>\\n                                <code>@BuildIn/Tool</code>\\n                                <props>\\n                                    <name>search_company_by_keywords</name>\\n                                    <status>{{state.search_company_by_keywords}}</status>\\n                                    <result>{{state.search_company_by_keywords_result}}</result>\\n                                </props>\\n                            </widget>\\n                        </message-embedded>"}

data: {"event_id": 2, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 1, "is_last": false, "data": "<message-embedded>\\n                                    <state>\\n                                        <set>\\n                                            <strategy>replace</strategy>\\n                                            <path>search_company_by_keywords</path>\\n                                            <value>success</value>\\n                                        </set>\\n                                        <set>\\n                                            <strategy>replace</strategy>\\n                                            <path>search_company_by_keywords_result</path>\\n                                            <value></value>\\n                                        </set>\\n                                    </state>\\n                                </message-embedded>"}

data: {"event_id": 3, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 2, "is_last": false, "data": "<message-embedded>\\n                            <state>\\n                                <set>\\n                                    <strategy>replace</strategy>\\n                                    <path>get_company_detail</path>\\n                                    <value>loading</value>\\n                                </set>\\n                                <set>\\n                                    <strategy>replace</strategy>\\n                                    <path>get_company_detail_result</path>\\n                                    <value></value>\\n                                </set>\\n                            </state>\\n                            <widget>\\n                                <code>@BuildIn/Tool</code>\\n                                <props>\\n                                    <name>get_company_detail</name>\\n                                    <status>{{state.get_company_detail}}</status>\\n                                    <result>{{state.get_company_detail_result}}</result>\\n                                </props>\\n                            </widget>\\n                        </message-embedded>"}

data: {"event_id": 4, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 3, "is_last": false, "data": "\\n          <message-embedded>\\n            <state>\\n              <set>\\n                <strategy>replace</strategy>\\n                <path>thoughtChainItems</path>\\n                <value>[]</value>\\n              </set>\\n            </state>\\n            <widget>\\n              <code>@BuildIn/ThoughtChain</code>\\n              <props>\\n                <items>{{state.thoughtChainItems}}</newItem>\\n              </props>\\n            </widget>\\n          </message-embedded>\\n          "}

data: {"event_id": 5, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 4, "is_last": false, "data": "\\n              <message-embedded>\\n                <state>\\n                  <set>\\n                    <strategy>replace</strategy>\\n                    <path>thoughtChainItems[0]</path>\\n                    <value>\\n                    {\\n                        \\"id\\": \\"0\\",\\n                        \\"title\\": \\"\u6570\u636e\u6536\u96c6\\",\\n                        \\"description\\": \\"\u3010get_company_detail\u3011\\",\\n                        \\"content\\": \\"\u7528\u6237\u7684\u95ee\u9898\u662f:[\u67e5\u4e2d\u8bc1\u4fe1\u7528\u7684\u6ce8\u518c\u8d44\u672c]\\",\\n                        \\"status\\": \\"pending\\"\\n                    }\\n                    </value>\\n                  </set>\\n                </state>\\n              </message-embedded>\\n              "}

data: {"event_id": 6, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 5, "is_last": false, "data": "\\n            <message-embedded>\\n              <state>\\n                <set>\\n                  <strategy>incrementalMerge</strategy>\\n                  <path>thoughtChainItems[0].content</path>\\n                  <value>LLM\u4f20\u5165\u7684\u53c2\u6570\u4e3a\u3010{&apos;query&apos;: {&apos;id&apos;: &apos;239589&apos;, &apos;service&apos;: &apos;company&apos;}}\u3011&#13;&#10;</value>\\n                </set>\\n              </state>\\n            </message-embedded>\\n        "}

data: {"event_id": 7, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 6, "is_last": false, "data": "\\n            <message-embedded>\\n              <state>\\n                <set>\\n                  <strategy>incrementalMerge</strategy>\\n                  <path>thoughtChainItems[0].content</path>\\n                  <value>\u4ece\u7f13\u5b58\u4e2d\u83b7\u53d6\u7ed3\u679c&#13;&#10;</value>\\n                </set>\\n              </state>\\n            </message-embedded>\\n        "}

data: {"event_id": 8, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 7, "is_last": false, "data": "\\n            <message-embedded>\\n              <state>\\n                <set>\\n                  <strategy>incrementalMerge</strategy>\\n                  <path>thoughtChainItems[0].content</path>\\n                  <value>\u5de5\u5177\u6267\u884c\u5b8c\u6bd5,\u63d0\u53d6\u5185\u5bb9\u4e2d...&#13;&#10;</value>\\n                </set>\\n              </state>\\n            </message-embedded>\\n        "}

data: {"event_id": 9, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 8, "is_last": false, "data": "\\n          <message-embedded>\\n            <state>\\n              <set>\\n                <strategy>replace</strategy>\\n                <path>thoughtChainItems[0].status</path>\\n                <value>success</value>\\n              </set>\\n            </state>\\n          </message-embedded>\\n        "}

data: {"event_id": 10, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 9, "is_last": false, "data": "\\n              <message-embedded>\\n                <state>\\n                  <set>\\n                    <strategy>replace</strategy>\\n                    <path>thoughtChainItems[1]</path>\\n                    <value>\\n                    {\\n                        \\"id\\": \\"1\\",\\n                        \\"title\\": \\"\u7ed8\u5236\u8868\u683c\\",\\n                        \\"description\\": \\"\u4e3a\u3010get_company_detail\u3011\u7ed8\u5236HTML\u548cMarkdown\u8868\u683c\\",\\n                        \\"content\\": \\"\u5f00\u59cb\u5904\u7406...\\",\\n                        \\"status\\": \\"pending\\"\\n                    }\\n                    </value>\\n                  </set>\\n                </state>\\n              </message-embedded>\\n              "}

data: {"event_id": 11, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 10, "is_last": false, "data": "<div class='html_table' id='5d8b9298-de45-470a-91ab-9246f48a794c'>"}

data: {"event_id": 12, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 11, "is_last": false, "data": "<table style=\\"max-height: 500px; overflow-y: scroll;\\"><thead><tr>data</tr><tr><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">id</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">name</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyGlobalId</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">isCore</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyStatus</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyCode</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyNameCn</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyShortName</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyNameEn</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">legRepresent</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">chairman</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">generalManager</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">boardSecretary</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">organizationForm</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">organizationFormDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">organizationCode</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCapital</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCapitalCurrency</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCapitalCurrencyDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerGovernment</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerAddress</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCountry</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCountryDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerRegion</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerRegionDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCity</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerCityDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">businessLicenseNumber</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">nationalTaxNumber</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">localTaxNumber</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">sourceId</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">sourceCode</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">deleteFlag</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">createBy</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">createTime</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">foundDate</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">registerDate</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">actualCapital</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyAddress</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyZipCode</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyPhone</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyFax</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyEmail</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyWeb</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">businessScope</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">mainBusiness</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">employNumber</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">infoUrl</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">infoNews</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">accountingFirm</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">legalAdvisor</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyState</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyStateDesc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">companyProfile</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">businessTermStart</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">businessTermEnd</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">revokeDate</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">controllerId</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">controllerName</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">firstShareholderId</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">firstShareholderName</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">firstShareholderRatio</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">csrcIndustryLevel1</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">csrcIndustryLevel1Desc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">csrcIndustryLevel2</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">csrcIndustryLevel2Desc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel1</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel1Desc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel2</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel2Desc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel3</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">swsIndustryLevel3Desc</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">categoryName1</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">categoryName2</th><th style=\\"white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;position: sticky;top: 0;z-index: 1; background: #CCC;\\">categoryName3</th></tr></thead><tbody>"}

data: {"event_id": 13, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 12, "is_last": false, "data": "<tr>"}

data: {"event_id": 14, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 13, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='239589'>239,589</td>"}

data: {"event_id": 15, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 14, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8'>\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8</td>"}

data: {"event_id": 16, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 15, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='239589'>239,589</td>"}

data: {"event_id": 17, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 16, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 18, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 17, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='12'>12</td>"}

data: {"event_id": 19, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 18, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='91440300342642396Y.CP'>91440300342642396Y.CP</td>"}

data: {"event_id": 20, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 19, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8'>\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8</td>"}

data: {"event_id": 21, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 20, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4e2d\u8bc1\u4fe1\u7528'>\u4e2d\u8bc1\u4fe1\u7528</td>"}

data: {"event_id": 22, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 21, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='China Securities Credit Investment Co., Ltd.'>China Securities Credit Investment Co., Ltd.</td>"}

data: {"event_id": 23, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 22, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u725b\u51a0\u5174'>\u725b\u51a0\u5174</td>"}

data: {"event_id": 24, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 23, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u725b\u51a0\u5174'>\u725b\u51a0\u5174</td>"}

data: {"event_id": 25, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 24, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u51af\u8f9e'>\u51af\u8f9e</td>"}

data: {"event_id": 26, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 25, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5f20\u5251\u6587'>\u5f20\u5251\u6587</td>"}

data: {"event_id": 27, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 26, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='3095'>3,095</td>"}

data: {"event_id": 28, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 27, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u80a1\u4efd\u6709\u9650\u516c\u53f8'>\u80a1\u4efd\u6709\u9650\u516c\u53f8</td>"}

data: {"event_id": 29, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 28, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='91440300342642396Y'>91440300342642396Y</td>"}

data: {"event_id": 30, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 29, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='458598.0'>458,598</td>"}

data: {"event_id": 31, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 30, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='RMB'>RMB</td>"}

data: {"event_id": 32, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 31, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4eba\u6c11\u5e01'>\u4eba\u6c11\u5e01</td>"}

data: {"event_id": 33, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 32, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u6df1\u5733\u5e02\u5e02\u573a\u76d1\u7763\u7ba1\u7406\u5c40'>\u6df1\u5733\u5e02\u5e02\u573a\u76d1\u7763\u7ba1\u7406\u5c40</td>"}

data: {"event_id": 34, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 33, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u5357\u5c71\u8857\u9053\u6842\u6e7e\u4e94\u8def128\u53f7\u57fa\u91d1\u5c0f\u9547\u5bf9\u51b2\u57fa\u91d1\u4e2d\u5fc3513'>\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u5357\u5c71\u8857\u9053\u6842\u6e7e\u4e94\u8def128\u53f7\u57fa\u91d1\u5c0f\u9547\u5bf9\u51b2\u57fa\u91d1\u4e2d\u5fc3513</td>"}

data: {"event_id": 35, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 34, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='CN'>CN</td>"}

data: {"event_id": 36, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 35, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4e2d\u56fd'>\u4e2d\u56fd</td>"}

data: {"event_id": 37, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 36, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='440000'>440,000</td>"}

data: {"event_id": 38, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 37, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5e7f\u4e1c\u7701'>\u5e7f\u4e1c\u7701</td>"}

data: {"event_id": 39, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 38, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='440300'>440,300</td>"}

data: {"event_id": 40, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 39, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u6df1\u5733\u5e02'>\u6df1\u5733\u5e02</td>"}

data: {"event_id": 41, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 40, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='440301112974921'>440,301,112,974,921</td>"}

data: {"event_id": 42, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 41, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 43, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 42, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 44, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 43, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='239589'>239,589</td>"}

data: {"event_id": 45, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 44, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='CSCS'>CSCS</td>"}

data: {"event_id": 46, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 45, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='0'>0</td>"}

data: {"event_id": 47, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 46, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='1101'>1,101</td>"}

data: {"event_id": 48, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 47, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='2021-11-29 17:44:14'>2021-11-29 17:44:14</td>"}

data: {"event_id": 49, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 48, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='2015-05-27'>2015-05-27</td>"}

data: {"event_id": 50, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 49, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='2022-04-07'>2022-04-07</td>"}

data: {"event_id": 51, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 50, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='458598.0'>458,598</td>"}

data: {"event_id": 52, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 51, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u5357\u5c71\u8857\u9053\u6842\u6e7e\u4e94\u8def128\u53f7\u57fa\u91d1\u5c0f\u9547\u5bf9\u51b2\u57fa\u91d1\u4e2d\u5fc3513'>\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u5357\u5c71\u8857\u9053\u6842\u6e7e\u4e94\u8def128\u53f7\u57fa\u91d1\u5c0f\u9547\u5bf9\u51b2\u57fa\u91d1\u4e2d\u5fc3513</td>"}

data: {"event_id": 53, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 52, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='518038'>518,038</td>"}

data: {"event_id": 54, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 53, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='0755-84362888'>0755-84362888</td>"}

data: {"event_id": 55, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 54, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='0755-83653315'>0755-83653315</td>"}

data: {"event_id": 56, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 55, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='<EMAIL>'><EMAIL></td>"}

data: {"event_id": 57, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 56, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 58, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 57, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u4e00\u822c\u7ecf\u8425\u9879\u76ee\u662f\uff1a\u6280\u672f\u670d\u52a1\u3001\u6280\u672f\u5f00\u53d1\u3001\u6280\u672f\u54a8\u8be2\u3001\u6280\u672f\u4ea4\u6d41\u3001\u6280\u672f\u8f6c\u8ba9\u3001\u6280\u672f\u63a8\u5e7f\uff1b\u4e92\u8054\u7f51\u6570\u636e\u670d\u52a1\uff1b\u6570\u636e\u5904\u7406\u548c\u5b58\u50a8\u652f\u6301\u670d\u52a1\uff1b\u8ba1\u7b97\u673a\u7cfb\u7edf\u670d\u52a1\uff1b\u4fe1\u606f\u7cfb\u7edf\u96c6\u6210\u670d\u52a1\uff1b\u4fe1\u606f\u6280\u672f\u54a8\u8be2\u670d\u52a1\u3002\uff08\u9664\u4f9d\u6cd5\u987b\u7ecf\u6279\u51c6\u7684\u9879\u76ee\u5916\uff0c\u51ed\u8425\u4e1a\u6267\u7167\u4f9d\u6cd5\u81ea\u4e3b\u5f00\u5c55\u7ecf\u8425\u6d3b\u52a8\uff09\uff0c\u8bb8\u53ef\u7ecf\u8425\u9879\u76ee\u662f\uff1a\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb\uff1b\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7\uff1b\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44\uff1b\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613\uff1b\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406\uff1b\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2\uff1b\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002'>\u4e00\u822c\u7ecf\u8425\u9879\u76ee\u662f\uff1a\u6280\u672f\u670d\u52a1\u3001\u6280\u672f\u5f00\u53d1\u3001\u6280\u672f\u54a8\u8be2\u3001\u6280\u672f\u4ea4\u6d41\u3001\u6280\u672f\u8f6c\u8ba9\u3001\u6280\u672f\u63a8\u5e7f\uff1b\u4e92\u8054\u7f51\u6570\u636e\u670d\u52a1\uff1b\u6570\u636e\u5904\u7406\u548c\u5b58\u50a8\u652f\u6301\u670d\u52a1\uff1b\u8ba1\u7b97\u673a\u7cfb\u7edf\u670d\u52a1\uff1b\u4fe1\u606f\u7cfb\u7edf\u96c6\u6210\u670d\u52a1\uff1b\u4fe1\u606f\u6280\u672f\u54a8\u8be2\u670d\u52a1\u3002\uff08\u9664\u4f9d\u6cd5\u987b\u7ecf\u6279\u51c6\u7684\u9879\u76ee\u5916\uff0c\u51ed\u8425\u4e1a\u6267\u7167\u4f9d\u6cd5\u81ea\u4e3b\u5f00\u5c55\u7ecf\u8425\u6d3b\u52a8\uff09\uff0c\u8bb8\u53ef\u7ecf\u8425\u9879\u76ee\u662f\uff1a\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb\uff1b\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7\uff1b\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44\uff1b\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613\uff1b\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406\uff1b\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2\uff1b\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002</td>"}

data: {"event_id": 59, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 58, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb;\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7;\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44;\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613;\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406;\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2;\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002\\n'>\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb;\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7;\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44;\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613;\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406;\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2;\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002</td>"}

data: {"event_id": 60, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 59, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 61, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 60, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 62, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 61, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 63, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 62, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5b89\u6c38\u534e\u660e\u4f1a\u8ba1\u5e08\u4e8b\u52a1\u6240(\u7279\u6b8a\u666e\u901a\u5408\u4f19)'>\u5b89\u6c38\u534e\u660e\u4f1a\u8ba1\u5e08\u4e8b\u52a1\u6240(\u7279\u6b8a\u666e\u901a\u5408\u4f19)</td>"}

data: {"event_id": 64, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 63, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5317\u4eac\u5e02\u91d1\u675c\u5f8b\u5e08\u4e8b\u52a1\u6240'>\u5317\u4eac\u5e02\u91d1\u675c\u5f8b\u5e08\u4e8b\u52a1\u6240</td>"}

data: {"event_id": 65, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 64, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='12'>12</td>"}

data: {"event_id": 66, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 65, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5b58\u7eed(\u5728\u8425\u3001\u5f00\u4e1a\u3001\u5728\u518c)'>\u5b58\u7eed(\u5728\u8425\u3001\u5f00\u4e1a\u3001\u5728\u518c)</td>"}

data: {"event_id": 67, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 66, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='    \u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8\u6210\u7acb\u4e8e2015\u5e7405\u670827\u65e5,\u6ce8\u518c\u5730\u4f4d\u4e8e\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u524d\u6e7e\u4e00\u8def1\u53f7A\u680b201\u5ba4(\u5165\u9a7b\u6df1\u5733\u5e02\u524d\u6d77\u5546\u52a1\u79d8\u4e66\u6709\u9650\u516c\u53f8),\u6cd5\u4eba\u4ee3\u8868\u4e3a\u725b\u51a0\u5174\u3002\u7ecf\u8425\u8303\u56f4\u5305\u62ec\u4e00\u822c\u7ecf\u8425\u9879\u76ee\u662f:,\u8bb8\u53ef\u7ecf\u8425\u9879\u76ee\u662f:\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb;\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7;\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44;\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613;\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406;\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2;\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8\u5bf9\u5916\u6295\u8d4417\u5bb6\u516c\u53f8,\u5177\u67091\u5904\u5206\u652f\u673a\u6784\u3002\\n'>\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8\u6210\u7acb\u4e8e2015\u5e7405\u670827\u65e5,\u6ce8\u518c\u5730\u4f4d\u4e8e\u6df1\u5733\u5e02\u524d\u6d77\u6df1\u6e2f\u5408\u4f5c\u533a\u524d\u6e7e\u4e00\u8def1\u53f7A\u680b201\u5ba4(\u5165\u9a7b\u6df1\u5733\u5e02\u524d\u6d77\u5546\u52a1\u79d8\u4e66\u6709\u9650\u516c\u53f8),\u6cd5\u4eba\u4ee3\u8868\u4e3a\u725b\u51a0\u5174\u3002\u7ecf\u8425\u8303\u56f4\u5305\u62ec\u4e00\u822c\u7ecf\u8425\u9879\u76ee\u662f:,\u8bb8\u53ef\u7ecf\u8425\u9879\u76ee\u662f:\u5404\u7c7b\u4fe1\u7528\u4e3b\u4f53\u53ca\u503a\u9879\u4ea7\u54c1\u4fe1\u7528\u589e\u8fdb;\u5f81\u4fe1\u4e1a\u52a1\u548c\u4fe1\u7528\u8bc4\u7ea7;\u80a1\u6743\u3001\u503a\u5238\u53ca\u91d1\u878d\u884d\u751f\u54c1\u6295\u8d44;\u589e\u4fe1\u4ea7\u54c1\u7684\u521b\u8bbe\u4e0e\u4ea4\u6613;\u589e\u4fe1\u57fa\u91d1\u8bbe\u7acb\u4e0e\u8fd0\u8425\u7ba1\u7406;\u4fe1\u7528\u53d7\u6258\u7ba1\u7406\u53ca\u54a8\u8be2;\u5176\u4ed6\u4e0e\u4fe1\u7528\u589e\u8fdb\u76f8\u5173\u7684\u79c1\u52df\u6295\u8d44\u4e1a\u52a1\u7b49\u3002\u4e2d\u8bc1\u4fe1\u7528\u589e\u8fdb\u80a1\u4efd\u6709\u9650\u516c\u53f8\u5bf9\u5916\u6295\u8d4417\u5bb6\u516c\u53f8,\u5177\u67091\u5904\u5206\u652f\u673a\u6784\u3002</td>"}

data: {"event_id": 68, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 67, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='2015-05-27'>2015-05-27</td>"}

data: {"event_id": 69, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 68, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='5000-01-01'>5000-01-01</td>"}

data: {"event_id": 70, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 69, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 71, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 70, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 72, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 71, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 73, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 72, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 74, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 73, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 75, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 74, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 76, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 75, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='L'>L</td>"}

data: {"event_id": 77, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 76, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u79df\u8d41\u548c\u5546\u52a1\u670d\u52a1\u4e1a'>\u79df\u8d41\u548c\u5546\u52a1\u670d\u52a1\u4e1a</td>"}

data: {"event_id": 78, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 77, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='72'>72</td>"}

data: {"event_id": 79, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 78, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5546\u52a1\u670d\u52a1\u4e1a'>\u5546\u52a1\u670d\u52a1\u4e1a</td>"}

data: {"event_id": 80, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 79, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 81, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 80, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 82, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 81, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 83, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 82, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 84, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 83, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 85, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 84, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 86, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 85, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u91d1\u878d\u4e1a'>\u91d1\u878d\u4e1a</td>"}

data: {"event_id": 87, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 86, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='\u5176\u4ed6\u91d1\u878d\u4e1a'>\u5176\u4ed6\u91d1\u878d\u4e1a</td>"}

data: {"event_id": 88, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 87, "is_last": false, "data": "<td style='white-space: nowrap;overflow: hidden;text-overflow: ellipsis; width:100px; max-width:200px;' title='None'>None</td>"}

data: {"event_id": 89, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 88, "is_last": false, "data": "</tr>"}

data: {"event_id": 90, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 89, "is_last": false, "data": "</tbody></table></div>"}

data: {"event_id": 91, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 90, "is_last": false, "data": "\\n            <message-embedded>\\n              <state>\\n                <set>\\n                  <strategy>incrementalMerge</strategy>\\n                  <path>thoughtChainItems[1].content</path>\\n                  <value>\u5904\u7406\u5b8c\u6bd5</value>\\n                </set>\\n              </state>\\n            </message-embedded>\\n        "}

data: {"event_id": 92, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 91, "is_last": false, "data": "\\n          <message-embedded>\\n            <state>\\n              <set>\\n                <strategy>replace</strategy>\\n                <path>thoughtChainItems[1].status</path>\\n                <value>success</value>\\n              </set>\\n            </state>\\n          </message-embedded>\\n        "}

data: {"event_id": 93, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 92, "is_last": false, "data": "<message-embedded>\\n                                    <state>\\n                                        <set>\\n                                            <strategy>replace</strategy>\\n                                            <path>get_company_detail</path>\\n                                            <value>success</value>\\n                                        </set>\\n                                        <set>\\n                                            <strategy>replace</strategy>\\n                                            <path>get_company_detail_result</path>\\n                                            <value></value>\\n                                        </set>\\n                                    </state>\\n                                </message-embedded>"}

data: {"event_id": 94, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 93, "is_last": false, "data": "\u4e2d"}

data: {"event_id": 95, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 94, "is_last": false, "data": "\u8bc1"}

data: {"event_id": 96, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 95, "is_last": false, "data": "\u4fe1\u7528"}

data: {"event_id": 97, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 96, "is_last": false, "data": "\u589e\u8fdb"}

data: {"event_id": 98, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 97, "is_last": false, "data": "\u80a1\u4efd\u6709\u9650\u516c\u53f8"}

data: {"event_id": 99, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 98, "is_last": false, "data": "\u7684"}

data: {"event_id": 100, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 99, "is_last": false, "data": "\u6ce8\u518c\u8d44\u672c"}

data: {"event_id": 101, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 100, "is_last": false, "data": "\u4e3a"}

data: {"event_id": 102, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 101, "is_last": false, "data": " **"}

data: {"event_id": 103, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 102, "is_last": false, "data": "458"}

data: {"event_id": 104, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 103, "is_last": false, "data": ","}

data: {"event_id": 105, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 104, "is_last": false, "data": "598"}

data: {"event_id": 106, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 105, "is_last": false, "data": "\u4e07\u5143"}

data: {"event_id": 107, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 106, "is_last": false, "data": "\u4eba\u6c11\u5e01"}

data: {"event_id": 108, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 107, "is_last": false, "data": "**"}

data: {"event_id": 109, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 108, "is_last": false, "data": "\u3002"}

data: {"event_id": 110, "event_type": 1001, "package_id": 1, "package_type": 0, "chunk_id": 109, "is_last": true, "data": " "}

data: {"event_id": 111, "event_type": 1002, "package_id": 2, "package_type": -1, "chunk_id": 0, "is_last": true, "data": null}



      `;
      const mocker = new MessageMocker(req, res, {
        length: 100,
        type: "streamed",
      });
      mocker.start(message);
    },
  },
]);

