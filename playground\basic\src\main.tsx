/// <reference types="vite/client" />
import "virtual:app-dep-info";
import "moment/dist/locale/zh-cn";
// 开发模式使用import引入css
import "@cscs-agent/core/dist/core-tailwind.css";
import "@cscs-agent/presets/dist/presets-tailwind.css";
import "@cscs-agent/agents/dist/agents-tailwind.css";
import "@cscs-agent/icons/dist/icons.css";

import "./styles.css";

import { ConfigProvider } from "antd";
import zhCN from "antd/es/locale/zh_CN";
import React from "react";

import { RouterConfig, initApp } from "@cscs-agent/core";
import { AgentHome, Chat, ConversationHistory, Login, defaultAuthGuard } from "@cscs-agent/presets";

import { config } from "./agent-config";
import Home from "./pages/home";

const routerConfig: RouterConfig = {
  pages: {
    home: {
      Component: Home,
    },
    chat: {
      Component: Chat,
    },
    agentHome: {
      Component: AgentHome,
    },
    login: {
      enable: true,
      Component: Login,
    },
    conversationHistory: {
      Component: ConversationHistory,
    },
  },
  authGuard: defaultAuthGuard,
  rootRoutes: [
    // {
    //   path: "message/test-solo",
    //   Component: SoloMessage,
    //   auth: true,
    // },
  ],
};

const Root: React.FC = (props) => {
  return <ConfigProvider locale={zhCN}>{props.children}</ConfigProvider>;
};

initApp({
  // loginUrl: import.meta.env.VITE_LOGIN_URL,
  loginUrl: "/login",
  routerConfig,
  agentChatConfig: config,
  Root,
}).then(() => {
  console.log("App initialized successfully");
});
