import React, { forwardRef, useEffect, useImperativeHandle, useState } from "react";

import {
  MessageRender as CoreMessageRender,
  IMessagePackage,
  Message,
  MessagePackageStatus,
  MessagePackageType,
  MessageReceiver,
  MessageReceiverEvent,
  MessageStatus,
  Role,
} from "@cscs-agent/core";

import { Transmitter } from "./transmitter";

const MessageRender = forwardRef<
  { runReq: () => void },
  {
    message?: string;
    agentCode: string;
    requestOptions: {
      url: string;
      method?: string;
      headers?: Record<string, string>;
      body?: Record<string, string>;
      params?: Record<string, string>;
    };
    onMessageChange?: (loading: boolean) => void;
  }
>(function Render(props, ref) {
  const { message, agentCode, requestOptions, onMessageChange } = props;
  const [loading, setLoading] = useState(false);
  const [$message, set$Message] = React.useState<Message | null>(null);
  const [runningTransmitter, setRunningTransmitter] = useState<Transmitter | null>(null);

  useImperativeHandle(ref, () => ({
    runReq: () => {
      if (loading) return;
      requestMessage();
    },
    cancelAndRunReq: () => {
      runningTransmitter?.cancel();
      setLoading(false);
    },
    reset: () => {
      set$Message(null);
    },
  }));

  useEffect(() => {
    if (message && typeof message === "string") {
      const messagePackage: IMessagePackage = {
        package_id: 0,
        package_type: MessagePackageType.Text,
        status: MessagePackageStatus.Finished,
        data: message,
      };
      try {
        const newMessage = new Message({
          id: "0",
          content: [messagePackage],
          role: Role.AI,
          agentCode,
        });
        set$Message(newMessage);
      } catch (error) {
        console.error(error);
      }
    }
  }, [message]);

  const requestMessage = () => {
    const { url, method, headers, body, params } = requestOptions;

    const $transmitter = new Transmitter(url, {
      headers,
      method,
    });

    setLoading(true);
    setRunningTransmitter($transmitter);

    // 发送消息
    $transmitter
      .send(params, body)
      .then((response) => {
        const messageReceiver = new MessageReceiver(response.data);

        messageReceiver.receive();

        // 监听接收到消息头事件
        messageReceiver.on(MessageReceiverEvent.HEADER_RECEIVED, () => {});

        // 监听消息完成事件
        messageReceiver.on(MessageReceiverEvent.MESSAGE_FINISHED, () => {
          set$Message((prev) => {
            if (prev) {
              return {
                ...prev,
                status: MessageStatus.Finished,
              } as Message;
            }
            return prev;
          });
          onMessageChange?.(false);
          setLoading(false);
        });

        // 监听正在接收消息包事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_RECEIVING, (packageObj: IMessagePackage) => {
          // 更新对应的消息
          try {
            const newMessage = new Message({
              id: "0",
              content: [packageObj],
              status: MessageStatus.Loading,
              role: Role.AI,
              agentCode,
            });
            set$Message((message) => {
              if (message) {
                return updateLoadingMessagePackage(message, packageObj);
              } else {
                return newMessage;
              }
            });
            onMessageChange?.(true);
          } catch (error) {
            console.error(error);
          }
        });

        // 监听消息包接收完成事件
        messageReceiver.on(MessageReceiverEvent.PACKAGE_FINISHED_RECEIVED, () => {
          // 如果没有 loading 状态的
        });

        // 监听消息包异常
        messageReceiver.on(MessageReceiverEvent.ERROR, () => {
          // 如果没有 loading 状态的
        });

        messageReceiver.on(MessageReceiverEvent.DONE, () => {
          // TODO 中断状态判断
        });
      })
      .catch((error) => {
        console.error(error);
        runningTransmitter?.cancel();
        setLoading(false);
        onMessageChange?.(false);
      });
  };

  return (
    <div className="message-render">
      {$message !== null && <CoreMessageRender data={$message} />}
      {loading && <div className="pts:mt-4 pts:message-loader"></div>}
    </div>
  );
});

function updateLoadingMessagePackage(message: Message, packageObj: IMessagePackage): Message {
  if (message && message.status === MessageStatus.Loading) {
    // 如果最后一条package的id等于当前消息的id且状态为loading，则更新package
    if (message.content.length > 0) {
      const lastPackage = message.content[message.content.length - 1];
      if (lastPackage.package_id === packageObj.package_id) {
        message.content.pop();
      }
    }
    // 插入最新的package
    message.content.push(packageObj);
  }
  return { ...message } as Message;
}

export default MessageRender;
