import React from "react";

interface FileListItem {
  id: string;
  name: string;
  status: FileUploadStatus;
  progress: number;
}

enum FileUploadStatus {
  PROCESSING = "PROCESSING",
  SUCCESS = "SUCCESS",
  ERROR = "ERROR",
  PENDING = "PENDING",
}

const RAGFileList: React.FC<{ data: FileListItem[] }> = (props) => {
  const { data = [] } = props;
  return (
    <div className="pts:bg-gray-50 pts:mb-4 pts:rounded-md pts:w-3/5">
      {data.map((i) => {
        const status = i.status;
        const progress = (i.progress * 100).toFixed(2);

        return (
          <div
            key={i.id}
            className="pts:flex pts:justify-between pts:items-center pts:px-4 pts:py-2 pts:border-gray-200 pts:not-last:border-b pts:text-sm"
          >
            <div className="pts:flex-1 pts:overflow-ellipsis pts:overflow-hidden pts:whitespace-nowrap">{i.name}</div>
            <div className="pts:text-xs">
              {status === FileUploadStatus.PROCESSING && (
                <div className="pts:text-black-45 pts:text-sm">处理中... {progress}%</div>
              )}
              {status === FileUploadStatus.SUCCESS && <div className="pts:text-black-25">完成</div>}
              {status === FileUploadStatus.ERROR && <div className="pts:text-red">处理失败</div>}
              {status === FileUploadStatus.PENDING && <div className="pts:text-black-45 pts:text-sm">等待处理</div>}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default RAGFileList;
