import React, { useMemo } from "react";

import { useActiveAgentConfig, useMessage } from "@/core";
import { Role } from "@/types";

/**
 * 消息追加组件 - 用于在消息底部动态渲染小部件
 * 支持内外两种放置位置（inside/outside），根据配置和位置条件过滤显示相应的小部件
 */
const Append: React.FC<{ role: Role; placement: "inside" | "outside" }> = (props) => {
  const { role, placement = "inside" } = props;
  // 获取当前活跃的代理配置
  const agentConfig = useActiveAgentConfig();
  // 获取当前消息上下文
  const { message } = useMessage();

  /**
   * 过滤有效的小部件
   * 根据放置位置、角色和隐藏条件过滤小部件：
   * 1. 如果小部件未指定placement，默认只在inside位置显示
   * 2. 如果指定了placement，只显示匹配位置的小部件
   * 3. 应用角色匹配和隐藏条件过滤
   */
  const widgets = useMemo(() => {
    return (
      agentConfig?.message?.slots?.append?.widgets.filter((i) => {
        // 获取隐藏条件，默认为始终显示
        const hidden = i.hidden ?? (() => false);
        // 位置匹配且角色匹配且未被隐藏：未指定placement时默认inside，否则按指定位置匹配
        return (
          message &&
          (!i.placement ? placement === "inside" : i.placement === placement) &&
          (!i.role || i.role === role) &&
          !hidden(message)
        );
      }) ?? []
    );
  }, [agentConfig, message, role, placement]);

  // 如果没有有效的小部件，不渲染任何内容
  if (widgets.length === 0) return null;

  // 渲染所有有效的小部件
  return (
    <>
      {widgets.map((Widget) => {
        // 使用小部件的code作为key，传递配置的props
        return <Widget.component key={Widget.code} {...Widget.props} />;
      })}
    </>
  );
};

export default Append;
