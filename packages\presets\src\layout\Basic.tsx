import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import React, { useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";

import { AgentList } from "@/components";
import { AgentChatContext, useAgentConfigs, useNavigate, useSubscribeCommand } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import { PresetsCommand } from "../command";
import { Conversation } from "../components";
import LogoHeader from "./LogoHeader";

interface DefaultLayoutProps {
  sidebar?: {
    agents: React.ReactNode;
    conversations: React.ReactNode;
  };
  main?: React.ReactNode;
  conversationOptions?: {
    height?: string;
  };
}

export const DefaultAgentLayoutContext = React.createContext({
  sideBarOpen: true,
});

const DefaultBasicLayout: React.FC<DefaultLayoutProps> = (props) => {
  const { sidebar, main, conversationOptions } = props;
  const agentConfigs = useAgentConfigs();
  const [sideBarOpen, setSideBarOpen] = useState(true);
  const { config } = useContext(AgentChatContext);
  const showAgentList = config?.showAgentList ?? true;
  const navigate = useNavigate();

  // 拖拽相关状态
  const [isResizing, setIsResizing] = useState(false);
  const [agentsHeight, setAgentsHeight] = useState<number | null>(null); // null 表示使用默认计算高度
  const sidebarRef = useRef<HTMLDivElement>(null);
  const startYRef = useRef(0);
  const startAgentsHeightRef = useRef(0);

  useSubscribeCommand(PresetsCommand.CloseSideBar, () => {
    setSideBarOpen(false);
  });

  useSubscribeCommand(PresetsCommand.OpenSideBar, () => {
    setSideBarOpen(true);
  });

  // 计算默认的 agents 区域高度
  const defaultAgentsHeight = useMemo(() => {
    if (!showAgentList) return 0;
    if (agentConfigs.length > 9) {
      return 425 - 112; // 425px 总高度减去 LogoHeader 高度
    } else {
      return 36 * agentConfigs.length;
    }
  }, [agentConfigs.length, showAgentList]);

  // 计算 conversations 区域高度
  const conversationsHeight = useMemo(() => {
    if (conversationOptions?.height) {
      return conversationOptions.height;
    }

    const currentAgentsHeight = agentsHeight ?? defaultAgentsHeight;
    // const totalAvailableHeight = sidebarRef.current?.clientHeight ?? 0;
    const logoHeaderHeight = 112; // LogoHeader 高度
    const dividerHeight = 1; // 分隔线高度

    return `calc(100% - ${logoHeaderHeight + currentAgentsHeight + dividerHeight}px)`;
  }, [agentsHeight, defaultAgentsHeight, conversationOptions?.height]);

  // 鼠标按下处理
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (!showAgentList) return;

      e.preventDefault();
      setIsResizing(true);
      startYRef.current = e.clientY;
      startAgentsHeightRef.current = agentsHeight ?? defaultAgentsHeight;

      // 防止文本选择
      document.body.style.userSelect = "none";
      document.body.style.cursor = "row-resize";
    },
    [showAgentList, agentsHeight, defaultAgentsHeight],
  );

  // 鼠标移动处理
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (!isResizing || !sidebarRef.current) return;

      const deltaY = e.clientY - startYRef.current;
      const newAgentsHeight = startAgentsHeightRef.current + deltaY;

      // 设置最小高度限制
      const minAgentsHeight = 105; // agents 区域最小高度
      const minConversationsHeight = 200; // conversations 区域最小高度
      const totalAvailableHeight = sidebarRef.current.clientHeight - 112 - 1; // 减去 LogoHeader 和分隔线高度
      const maxAgentsHeight = totalAvailableHeight - minConversationsHeight;

      const clampedHeight = Math.max(minAgentsHeight, Math.min(maxAgentsHeight, newAgentsHeight));
      setAgentsHeight(clampedHeight);
    },
    [isResizing],
  );

  // 鼠标释放处理
  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
    document.body.style.userSelect = "";
    document.body.style.cursor = "";
  }, []);

  // 添加全局事件监听器
  useEffect(() => {
    if (isResizing) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const agents = useMemo(() => {
    return sidebar?.agents ?? <AgentList />;
  }, [sidebar?.agents]);

  const conversations = useMemo(() => {
    return (
      sidebar?.conversations ?? (
        <Conversation
          height={conversationsHeight}
          extras={
            <Tooltip title="历史会话">
              <Button
                type="text"
                size="small"
                icon={<Icon icon="Search" />}
                onClick={() => navigate("/conversations")}
              />
            </Tooltip>
          }
        />
      )
    );
  }, [sidebar?.conversations, conversationsHeight, navigate]);

  const mainContent = useMemo(() => {
    return main ?? <div>main</div>;
  }, [main]);

  return (
    <DefaultAgentLayoutContext.Provider value={{ sideBarOpen }}>
      <div className="pts:flex pts:w-full pts:h-full">
        {/* Left sidebar - fixed width 260px */}
        <div
          ref={sidebarRef}
          className="pts:flex pts:flex-col pts:bg-light-gray pts:border-gray-200 pts:border-r pts:h-full pts:overflow-hidden pts:transition-all pts:duration-300"
          style={{
            width: sideBarOpen ? "260px" : "0",
            minWidth: sideBarOpen ? "260px" : "0",
          }}
        >
          <div className="pts:flex pts:flex-col pts:w-[260px] pts:h-full">
            <LogoHeader />
            {showAgentList && (
              <div
                className="pts:overflow-y-auto"
                style={{
                  height: agentsHeight ? `${agentsHeight}px` : "auto",
                  flexShrink: 0,
                }}
              >
                <div className="pts:pr-2 pts:pl-2">{agents}</div>
              </div>
            )}

            {/* 可拖拽的分隔条 */}
            {showAgentList && (
              <div
                className="pts:hover:bg-blue-100 pts:mx-2 pts:border-gray-200 pts:border-t pts:transition-colors pts:duration-200 pts:cursor-row-resize"
                style={{
                  height: "4px",
                  marginTop: "-2px",
                  marginBottom: "-2px",
                  position: "relative",
                }}
                onMouseDown={handleMouseDown}
              >
                {/* 扩展拖拽区域 */}
                <div
                  className="pts:-top-1 pts:right-0 pts:left-0 pts:absolute pts:h-2 pts:cursor-row-resize"
                  style={{ zIndex: 1 }}
                />
              </div>
            )}

            <div className="pts:flex-1 pts:pr-[2px] pts:overflow-hidden">{conversations}</div>
          </div>
        </div>

        {/* Middle content - adaptive width */}
        <div className="pts:flex-1 pts:h-full">{mainContent}</div>
      </div>
    </DefaultAgentLayoutContext.Provider>
  );
};

export default DefaultBasicLayout;
