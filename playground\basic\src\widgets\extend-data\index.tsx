import { Button } from "antd";
import React, { useEffect } from "react";

import { BuildInCommand, useCommandRunner, useMessages, useSubscribeCommand } from "@cscs-agent/core";

const AddExtendData: React.FC = () => {
  const runner = useCommandRunner();
  const [messages] = useMessages();

  useEffect(() => {
    // console.log(messages);
  }, [messages]);

  useSubscribeCommand(BuildInCommand.EmitMessageSendedEvent, () => {
    runner(BuildInCommand.UpdateExtendSenderData, {
      setValue: () => {
        return {};
      },
    });
  });

  return (
    <>
      <Button
        onClick={() =>
          runner(BuildInCommand.UpdateExtendSenderData, {
            setValue: (prevValue: any) => {
              const key = Math.random().toString();
              return prevValue ? { ...prevValue, [key]: 1 } : { [key]: 1 };
            },
          })
        }
        size="small"
        shape="round"
      >
        添加ExtendData
      </Button>
      <Button
        onClick={() =>
          runner(BuildInCommand.UpdateExtendSenderParams, {
            setValue: (prevValue: any) => {
              prevValue.connection_id = "aaaaaaaaaaaa";
              return prevValue;
            },
          })
        }
        size="small"
        shape="round"
      >
        添加ExtendParams
      </Button>
    </>
  );
};

export default AddExtendData;
