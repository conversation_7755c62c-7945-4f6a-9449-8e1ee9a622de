import { existsSync, mkdirSync, readFileSync, watch, writeFileSync } from "node:fs";
import { tmpdir } from "node:os";
import { dirname, resolve } from "node:path";
import { fileURLToPath } from "node:url";
import { pathToFileURL } from "node:url";
import { CommonServerOptions } from "vite";

import { Igniter } from "./index.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Types for proxy configuration
type ProxyConfig = CommonServerOptions["proxy"];

interface ProxyConfigModule {
  default?: ProxyConfig;
  config?: ProxyConfig;
}

// Cache for compiled proxy configurations
const proxyConfigCache = new Map<string, { config: ProxyConfig; timestamp: number }>();

export class ProxyConfigManager {
  private root: string;
  private igniter: Igniter;

  constructor(root: string, igniter: Igniter) {
    this.root = root;
    this.igniter = igniter;
  }

  /**
   * Dynamically load and compile proxy configuration from proxy.ts file
   *
   * This method provides runtime loading of TypeScript proxy configuration files with the following features:
   * - Automatic TypeScript compilation using esbuild
   * - Multiple search locations for proxy.ts files
   * - Caching of compiled results to avoid recompilation
   * - Comprehensive error handling and validation
   * - Support for both synchronous and asynchronous loading patterns
   *
   * Search locations (in order):
   * 1. Provided configPath parameter
   * 2. {root}/proxy.ts
   * 3. {root}/config/proxy.ts
   * 4. {root}/src/config/proxy.ts
   * 5. {cwd}/proxy.ts
   * 6. {cwd}/config/proxy.ts
   *
   * Expected proxy.ts file format:
   * ```typescript
   * import { CommonServerOptions } from "vite";
   *
   * const config: CommonServerOptions["proxy"] = {
   *   "^/api": {
   *     target: "http://localhost:8000",
   *     changeOrigin: true,
   *   },
   * };
   *
   * export default config;
   * ```
   *
   * @param configPath - Optional path to proxy.ts file (defaults to searching common locations)
   * @param useCache - Whether to use cached compiled result (default: true)
   * @returns Promise<ProxyConfig | null> - Proxy configuration object or null if not found
   * @throws Error if TypeScript compilation fails or configuration is invalid
   */
  async loadConfig(configPath?: string, useCache: boolean = true): Promise<ProxyConfig | null> {
    try {
      // Determine proxy.ts file path
      const proxyFilePath = configPath || resolve(this.root, "config/proxy.ts");

      // Check if proxy.ts file exists
      if (!existsSync(proxyFilePath)) {
        // Try alternative locations
        const alternativePaths = [
          resolve(this.root, "config", "proxy.ts"),
          resolve(this.root, "src", "config", "proxy.ts"),
          resolve(process.cwd(), "proxy.ts"),
          resolve(process.cwd(), "config", "proxy.ts"),
        ];

        let foundPath: string | null = null;
        for (const altPath of alternativePaths) {
          if (existsSync(altPath)) {
            foundPath = altPath;
            break;
          }
        }

        if (!foundPath) {
          console.warn(
            `Proxy configuration file not found. Searched paths: ${[proxyFilePath, ...alternativePaths].join(", ")}`,
          );
          return null;
        }

        // Update proxyFilePath to the found path
        const actualProxyFilePath = foundPath;

        return this.loadAndCompileConfig(actualProxyFilePath, useCache);
      }

      return this.loadAndCompileConfig(proxyFilePath, useCache);
    } catch (error) {
      console.error("Error loading proxy configuration:", error);
      return null;
    }
  }

  /**
   * 监听 proxy 文件变化
   * @param path 配置文件路径
   * @param onChange 文件变化时的回调函数
   */
  watchConfigFile(path: string, onChange?: () => void) {
    const watcher = watch(path, async () => {
      console.log("Proxy changed, please restart dev server manually.");
      watcher.close();
      if (onChange) {
        await onChange();
      }
    });
  }

  /**
   * Load and compile TypeScript proxy configuration file
   * @param filePath - Path to the proxy.ts file
   * @param useCache - Whether to use cached result
   * @returns Promise<ProxyConfig | null> - Compiled proxy configuration
   */
  private async loadAndCompileConfig(filePath: string, useCache: boolean): Promise<ProxyConfig | null> {
    this.watchConfigFile(filePath, () => {
      this.igniter.restart();
    });

    try {
      // Check cache first
      if (useCache && proxyConfigCache.has(filePath)) {
        const cached = proxyConfigCache.get(filePath)!;
        const fileStats = await import("node:fs/promises").then((fs) => fs.stat(filePath));

        // Check if cached version is still valid (file hasn't been modified)
        if (cached.timestamp >= fileStats.mtimeMs) {
          return cached.config;
        }
      }

      // Read the TypeScript file content
      const tsContent = readFileSync(filePath, "utf-8");

      // Compile TypeScript to JavaScript using esbuild (via dynamic import)
      const compiledJs = await this.compileTypeScript(tsContent, filePath);

      // Create a temporary file to load the compiled JavaScript
      const tempDir = tmpdir();
      const tempFileName = `proxy-config-${Date.now()}-${Math.random().toString(36).substring(2, 11)}.mjs`;
      const tempFilePath = resolve(tempDir, tempFileName);

      // Ensure temp directory exists
      if (!existsSync(tempDir)) {
        mkdirSync(tempDir, { recursive: true });
      }

      // Write compiled JavaScript to temporary file
      writeFileSync(tempFilePath, compiledJs, "utf-8");

      try {
        // Dynamically import the compiled module
        const moduleUrl = pathToFileURL(tempFilePath).href;
        const module = (await import(moduleUrl)) as ProxyConfigModule;

        // Extract proxy configuration from module
        const config = module.default || module.config;

        if (!config) {
          throw new Error(
            "Proxy configuration file must export a default configuration object or named 'config' export",
          );
        }

        // Validate configuration structure
        this.validateConfig(config);

        // Cache the result
        if (useCache) {
          const fileStats = await import("node:fs/promises").then((fs) => fs.stat(filePath));
          proxyConfigCache.set(filePath, {
            config,
            timestamp: fileStats.mtimeMs,
          });
        }

        return config;
      } finally {
        // Clean up temporary file
        try {
          await import("node:fs/promises").then((fs) => fs.unlink(tempFilePath));
        } catch (cleanupError) {
          console.warn("Failed to clean up temporary file:", tempFilePath, cleanupError);
        }
      }
    } catch (error) {
      console.error("Error compiling proxy configuration:", error);
      throw error;
    }
  }

  /**
   * Compile TypeScript content to JavaScript using esbuild
   * @param tsContent - TypeScript source code
   * @param filePath - Original file path (for error reporting)
   * @returns Promise<string> - Compiled JavaScript code
   */
  private async compileTypeScript(tsContent: string, filePath: string): Promise<string> {
    try {
      // Use esbuild to compile TypeScript to JavaScript
      const esbuild = await import("esbuild");

      const result = await esbuild.transform(tsContent, {
        loader: "ts",
        target: "es2022",
        format: "esm",
        sourcefile: filePath,
        keepNames: true,
        minify: false,
        sourcemap: false,
      });

      if (result.warnings.length > 0) {
        console.warn("TypeScript compilation warnings:", result.warnings);
      }

      return result.code;
    } catch (error) {
      console.error("TypeScript compilation failed:", error);
      throw new Error(
        `Failed to compile TypeScript proxy configuration: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  /**
   * Validate proxy configuration structure
   * @param config - Proxy configuration object to validate
   */
  private validateConfig(config: ProxyConfig): void {
    if (!config || typeof config !== "object") {
      throw new Error("Proxy configuration must be an object");
    }

    for (const [path, proxyOptions] of Object.entries(config)) {
      if (!proxyOptions || typeof proxyOptions !== "object") {
        throw new Error(`Proxy configuration for path "${path}" must be an object`);
      }

      if (!proxyOptions.target || typeof proxyOptions.target !== "string") {
        throw new Error(`Proxy configuration for path "${path}" must have a valid target URL`);
      }

      // Validate target URL format
      try {
        new URL(proxyOptions.target);
      } catch {
        throw new Error(`Proxy configuration for path "${path}" has invalid target URL: ${proxyOptions.target}`);
      }

      // Validate optional properties
      if (proxyOptions.changeOrigin !== undefined && typeof proxyOptions.changeOrigin !== "boolean") {
        throw new Error(`Proxy configuration for path "${path}" changeOrigin must be a boolean`);
      }

      if (proxyOptions.rewrite !== undefined && typeof proxyOptions.rewrite !== "function") {
        throw new Error(`Proxy configuration for path "${path}" rewrite must be a function`);
      }
    }
  }
}