import { Tooltip, message } from "antd";
import React, { useRef } from "react";

import { PresetsCommand } from "@/command";
import { BuildInCommand, useCommandRunner, useSubscribeCommand } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

interface UploadButtonProps {
  accept: string[];
}

const UploadButton: React.FC<UploadButtonProps> = (props) => {
  const { accept = [] } = props;
  const runner = useCommandRunner();
  const inputRef = useRef<HTMLInputElement | null>(null);
  const acceptStr = accept.join();

  const handleChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = filterAccept(e.target.files);
    if (!files) return;
    runner(PresetsCommand.UploadFiles, { files });
    runner(PresetsCommand.OpenUploadFileList);
    // 清空 input
    setTimeout(() => {
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    });
  };

  useSubscribeCommand(BuildInCommand.PasteFilesIntoSender, ({ files }) => {
    const acceptFiles = filterAccept(files);
    if (!acceptFiles) return;
    runner(PresetsCommand.UploadFiles, { files: acceptFiles });
    runner(PresetsCommand.OpenUploadFileList);
  });

  const filterAccept = (files: FileList | null) => {
    if (!files) return;

    // 文件数量限制：最多10个文件
    if (files.length > 10) {
      message.error("最多只能上传10个文件，请重新选择");
      return;
    }

    const acceptFiles: File[] = [];
    const notAcceptFiles: File[] = [];
    const oversizeFiles: File[] = [];
    const maxFileSize = 100 * 1024 * 1024; // 100MB

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // 文件大小限制：单个文件最大100MB
      if (file.size > maxFileSize) {
        oversizeFiles.push(file);
        continue;
      }

      const extension = file.name.split(".").pop()?.toLowerCase() ?? "unknown";
      if (accept.includes("." + extension)) {
        acceptFiles.push(file);
      } else {
        notAcceptFiles.push(file);
      }
    }

    // 显示错误提示信息
    if (oversizeFiles.length > 0) {
      message.error(`以下文件大小超过100MB限制：${oversizeFiles.map((f) => f.name).join(", ")}`);
    }

    if (notAcceptFiles.length > 0) {
      message.warn(
        "上传的文件包含暂未支持的文件格式，请转换文件格式后重试。支持PDF、DOC、XLSX、PPT、TXT、MD、SQL、图片等。",
      );
    }

    // 如果有超大文件，则不允许上传任何文件
    if (oversizeFiles.length > 0) {
      return;
    }

    return acceptFiles;
  };

  return (
    <Tooltip title="最多10个文件（最大100MB），支持jpg、jpeg、png等图片格式；PDF、Word、Excel、Markdown等文件格式">
      <div className="pts:relative pts:flex pts:justify-center pts:items-center pts:hover:bg-gray-100 pts:rounded-lg pts:w-8 pts:h-8">
        <Icon icon="Attachment" />
        <input
          type="file"
          onChange={handleChange}
          ref={inputRef}
          multiple={true}
          accept={acceptStr}
          className="pts:top-0 pts:left-0 pts:absolute pts:opacity-0 pts:w-8 pts:h-8 pts:cursor-pointer"
        />
      </div>
    </Tooltip>
  );
};

export default UploadButton;
