import chalk from "chalk";
import { resolve } from "node:path";
import { CommonServerOptions, InlineConfig, ViteDevServer, build, createServer } from "vite";

import baseConfig from "./base.config.js";
import { BuilderConfigManager } from "./builder-config-manager.js";
import { ProxyConfigManager } from "./proxy-config-manager.js";

export class Igniter {
  private root: string;
  server: ViteDevServer | null = null;
  config: InlineConfig = {};
  port = 3000;
  private builderConfigManager: BuilderConfigManager;
  private proxyConfigManager: ProxyConfigManager;

  constructor(root: string, port?: number) {
    this.root = root;
    if (port) {
      this.port = port;
    }
    this.builderConfigManager = new BuilderConfigManager(root);
    this.proxyConfigManager = new ProxyConfigManager(root, this);
  }

  async loadConfig() {
    const serverConfig = await this.getServerConfig();

    // 加载 BuilderConfig
    const builderConfig = await this.builderConfigManager.loadConfig();

    // 从 BuilderConfig 中提取 Vite 配置
    let builderViteConfig: InlineConfig = {};
    if (builderConfig) {
      builderViteConfig = await this.builderConfigManager.getViteConfig(builderConfig);
    }

    // 合并配置，优先级：builderViteConfig > baseConfig
    const mergedConfig = {
      ...baseConfig,
      root: resolve(this.root),
      forceOptimizeDeps: true,
      server: serverConfig,
    };

    // 处理 base 配置
    if (builderViteConfig.base !== undefined) {
      mergedConfig.base = builderViteConfig.base;
    }

    // 处理 alias 配置 - 需要深度合并
    if (builderViteConfig.resolve?.alias) {
      mergedConfig.resolve = {
        ...mergedConfig.resolve,
        alias: {
          ...mergedConfig.resolve?.alias,
          ...builderViteConfig.resolve.alias,
        },
      };
    }

    // 处理 plugins 配置
    if (builderConfig?.vite?.plugins?.plugins) {
      const { plugins: pluginConfig } = builderConfig.vite;

      if (pluginConfig.mode === "override") {
        // 覆盖模式：完全使用配置中的插件
        mergedConfig.plugins = pluginConfig.plugins;
      } else {
        // 追加模式（默认）：将插件添加到现有插件列表中
        const existingPlugins = Array.isArray(mergedConfig.plugins) ? mergedConfig.plugins : [];
        mergedConfig.plugins = [...existingPlugins, ...(pluginConfig.plugins || [])];
      }
    }

    this.config = mergedConfig;
  }

  async start(isRestart = false) {
    await this.loadConfig();
    // Load proxy configuration
    if (!this.config) {
      console.error("config is null");
      return;
    }
    this.server = await createServer(this.config);
    await this.server.listen(this.port, isRestart);
    if (!isRestart) {
      this.server.printUrls();
      this.server.bindCLIShortcuts({ print: true });
    }
  }

  async restart() {
    console.log(chalk.yellow("Restarting dev server..."));
    await this.server?.close();
    await this.start();
  }

  async build() {
    await build({
      root: resolve(this.root),
      ...baseConfig,
    });
  }

  async getServerConfig() {
    const proxy = await this.proxyConfigManager.loadConfig();
    return {
      proxy,
    } as CommonServerOptions;
  }
}
