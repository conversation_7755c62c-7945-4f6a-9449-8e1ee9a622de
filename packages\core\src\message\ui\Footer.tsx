import { Space } from "antd";
import React, { useMemo } from "react";

import { useActiveAgentConfig, useMessage } from "@/core";
import { Role } from "@/types";

/**
 * 消息页脚组件
 * 根据当前消息和角色配置动态渲染页脚小部件
 *
 * 主要功能：
 * - 从代理配置中获取页脚小部件列表
 * - 根据角色和隐藏条件过滤小部件
 * - 使用 Space 组件布局渲染小部件
 */
const Footer: React.FC<{ role: Role }> = (props) => {
  const { role } = props;
  const agentConfig = useActiveAgentConfig();
  const { message } = useMessage();

  /**
   * 过滤并获取适用的页脚小部件
   * 过滤条件：
   * - 小部件角色与当前角色匹配（或小部件没有角色限制）
   * - 小部件不满足隐藏条件
   *
   * @returns 过滤后的小部件数组
   */
  const widgets = useMemo(() => {
    return (
      agentConfig?.message?.slots?.footer?.widgets.filter((i) => {
        const hidden = i.hidden ?? (() => false);
        return message && (!i.role || i.role === role) && !hidden(message);
      }) ?? []
    );
  }, [agentConfig, message, role]);

  // 如果没有适用的小部件，不渲染页脚
  if (widgets.length === 0) return null;

  return (
    <Space className="ag:text-black-65">
      {widgets.map((Widget) => {
        return <Widget.component key={Widget.code} {...Widget.props} />;
      })}
    </Space>
  );
};

export default Footer;
