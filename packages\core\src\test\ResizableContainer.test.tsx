import { describe, expect, it } from 'vitest';

// 导入组件的类型定义进行测试
interface ResizableContainerProps {
  children: React.ReactNode;
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  height: number;
  className?: string;
  handlePosition?: "left" | "right";
}

// 测试拖拽逻辑的辅助函数
function calculateNewWidth(
  startWidth: number,
  deltaX: number,
  handlePosition: "left" | "right",
  minWidth: number,
  maxWidth: number
): number {
  const adjustedDelta = handlePosition === "left" ? -deltaX : deltaX;
  return Math.max(minWidth, Math.min(maxWidth, startWidth + adjustedDelta));
}

describe('ResizableContainer', () => {
  it('应该有正确的 TypeScript 接口定义', () => {
    // 测试接口的类型安全性
    const validProps: ResizableContainerProps = {
      children: 'test',
      height: 400,
      initialWidth: 300,
      minWidth: 100,
      maxWidth: 1000,
      className: 'test-class',
      handlePosition: 'right'
    };

    expect(validProps.handlePosition).toBe('right');
    expect(validProps.height).toBe(400);
    expect(validProps.initialWidth).toBe(300);
  });

  it('应该正确计算右侧手柄的拖拽宽度', () => {
    const startWidth = 300;
    const deltaX = 50; // 向右拖拽 50px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(350); // 300 + 50
  });

  it('应该正确计算左侧手柄的拖拽宽度', () => {
    const startWidth = 300;
    const deltaX = 50; // 向右拖拽 50px，但对于左侧手柄应该减少宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(250); // 300 - 50 (因为左侧手柄反转方向)
  });

  it('应该遵守最小宽度限制', () => {
    const startWidth = 150;
    const deltaX = -100; // 尝试减少 100px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(100); // 应该被限制在最小值
  });

  it('应该遵守最大宽度限制', () => {
    const startWidth = 950;
    const deltaX = 100; // 尝试增加 100px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(1000); // 应该被限制在最大值
  });

  it('应该为左侧手柄遵守最小宽度限制', () => {
    const startWidth = 150;
    const deltaX = 100; // 向右拖拽，对于左侧手柄会减少宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(100); // 150 - 100 = 50，但被限制在最小值 100
  });

  it('应该为左侧手柄遵守最大宽度限制', () => {
    const startWidth = 950;
    const deltaX = -100; // 向左拖拽，对于左侧手柄会增加宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(1000); // 950 + 100 = 1050，但被限制在最大值 1000
  });

  it('应该支持 handlePosition 的所有有效值', () => {
    const validPositions: Array<"left" | "right"> = ["left", "right"];

    validPositions.forEach(position => {
      expect(["left", "right"]).toContain(position);
    });
  });

  it('应该有合理的默认值', () => {
    // 测试默认值的合理性
    const defaultProps = {
      initialWidth: 300,
      minWidth: 100,
      maxWidth: 1000,
      handlePosition: "right" as const
    };

    expect(defaultProps.initialWidth).toBeGreaterThanOrEqual(defaultProps.minWidth);
    expect(defaultProps.initialWidth).toBeLessThanOrEqual(defaultProps.maxWidth);
    expect(defaultProps.minWidth).toBeLessThan(defaultProps.maxWidth);
    expect(["left", "right"]).toContain(defaultProps.handlePosition);
  });
});
