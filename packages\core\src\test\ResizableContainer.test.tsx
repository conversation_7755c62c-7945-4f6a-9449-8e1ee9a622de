import { describe, expect, it } from 'vitest';

// 导入组件的类型定义进行测试
interface ResizableContainerProps {
  // 向后兼容的单列模式属性
  children?: React.ReactNode;
  initialWidth?: number;
  minWidth?: number;
  maxWidth?: number;
  height?: number;
  className?: string;
  handlePosition?: "left" | "right";

  // 新的两列布局属性
  leftContent?: React.ReactNode;
  rightContent?: React.ReactNode;
  initialLeftWidth?: number | string; // 支持像素值或百分比
  minLeftWidth?: number;
  maxLeftWidth?: number;

  // 保留旧的属性名以确保向后兼容
  leftCol?: React.ReactNode;
  rightCol?: React.ReactNode;
}

// 测试拖拽逻辑的辅助函数（单列模式）
function calculateNewWidth(
  startWidth: number,
  deltaX: number,
  handlePosition: "left" | "right",
  minWidth: number,
  maxWidth: number
): number {
  const adjustedDelta = handlePosition === "left" ? -deltaX : deltaX;
  return Math.max(minWidth, Math.min(maxWidth, startWidth + adjustedDelta));
}

// 测试两列模式拖拽逻辑的辅助函数
function calculateNewLeftWidth(
  startLeftWidthPercent: number,
  deltaX: number,
  containerWidth: number,
  minLeftWidth: number,
  maxLeftWidth: number
): number {
  const startLeftWidthPx = (startLeftWidthPercent / 100) * containerWidth;
  const newLeftWidthPx = Math.max(minLeftWidth, Math.min(maxLeftWidth, startLeftWidthPx + deltaX));
  return (newLeftWidthPx / containerWidth) * 100;
}

// 测试初始宽度解析的辅助函数
function parseInitialLeftWidth(initialLeftWidth: number | string): number {
  if (typeof initialLeftWidth === 'string' && initialLeftWidth.endsWith('%')) {
    return parseFloat(initialLeftWidth);
  }
  return typeof initialLeftWidth === 'number' ? initialLeftWidth : 300;
}

describe('ResizableContainer', () => {
  describe('单列模式（向后兼容）', () => {
    it('应该有正确的 TypeScript 接口定义', () => {
      // 测试接口的类型安全性
      const validProps: ResizableContainerProps = {
        children: 'test',
        height: 400,
        initialWidth: 300,
        minWidth: 100,
        maxWidth: 1000,
        className: 'test-class',
        handlePosition: 'right'
      };

      expect(validProps.handlePosition).toBe('right');
      expect(validProps.height).toBe(400);
      expect(validProps.initialWidth).toBe(300);
    });

  it('应该正确计算右侧手柄的拖拽宽度', () => {
    const startWidth = 300;
    const deltaX = 50; // 向右拖拽 50px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(350); // 300 + 50
  });

  it('应该正确计算左侧手柄的拖拽宽度', () => {
    const startWidth = 300;
    const deltaX = 50; // 向右拖拽 50px，但对于左侧手柄应该减少宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(250); // 300 - 50 (因为左侧手柄反转方向)
  });

  it('应该遵守最小宽度限制', () => {
    const startWidth = 150;
    const deltaX = -100; // 尝试减少 100px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(100); // 应该被限制在最小值
  });

  it('应该遵守最大宽度限制', () => {
    const startWidth = 950;
    const deltaX = 100; // 尝试增加 100px
    const handlePosition = "right";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(1000); // 应该被限制在最大值
  });

  it('应该为左侧手柄遵守最小宽度限制', () => {
    const startWidth = 150;
    const deltaX = 100; // 向右拖拽，对于左侧手柄会减少宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(100); // 150 - 100 = 50，但被限制在最小值 100
  });

  it('应该为左侧手柄遵守最大宽度限制', () => {
    const startWidth = 950;
    const deltaX = -100; // 向左拖拽，对于左侧手柄会增加宽度
    const handlePosition = "left";
    const minWidth = 100;
    const maxWidth = 1000;

    const newWidth = calculateNewWidth(startWidth, deltaX, handlePosition, minWidth, maxWidth);
    expect(newWidth).toBe(1000); // 950 + 100 = 1050，但被限制在最大值 1000
  });

  it('应该支持 handlePosition 的所有有效值', () => {
    const validPositions: Array<"left" | "right"> = ["left", "right"];

    validPositions.forEach(position => {
      expect(["left", "right"]).toContain(position);
    });
  });

  it('应该有合理的默认值', () => {
    // 测试默认值的合理性
    const defaultProps = {
      initialWidth: 300,
      minWidth: 100,
      maxWidth: 1000,
      handlePosition: "right" as const
    };

    expect(defaultProps.initialWidth).toBeGreaterThanOrEqual(defaultProps.minWidth);
    expect(defaultProps.initialWidth).toBeLessThanOrEqual(defaultProps.maxWidth);
    expect(defaultProps.minWidth).toBeLessThan(defaultProps.maxWidth);
    expect(["left", "right"]).toContain(defaultProps.handlePosition);
  });
  });

  describe('两列模式', () => {
    it('应该有正确的两列模式 TypeScript 接口定义', () => {
      // 测试两列模式的接口类型安全性
      const validTwoColumnProps: ResizableContainerProps = {
        leftContent: 'Left content',
        rightContent: 'Right content',
        initialLeftWidth: '60%',
        minLeftWidth: 200,
        maxLeftWidth: 800,
        height: 400,
        className: 'test-class'
      };

      expect(validTwoColumnProps.leftContent).toBe('Left content');
      expect(validTwoColumnProps.rightContent).toBe('Right content');
      expect(validTwoColumnProps.initialLeftWidth).toBe('60%');
      expect(validTwoColumnProps.minLeftWidth).toBe(200);
      expect(validTwoColumnProps.maxLeftWidth).toBe(800);
    });

    it('应该正确解析百分比形式的初始左列宽度', () => {
      const percentWidth = parseInitialLeftWidth('60%');
      expect(percentWidth).toBe(60);
    });

    it('应该正确解析数字形式的初始左列宽度', () => {
      const pixelWidth = parseInitialLeftWidth(300);
      expect(pixelWidth).toBe(300);
    });

    it('应该正确计算两列模式的拖拽宽度', () => {
      const startLeftWidthPercent = 50; // 50%
      const deltaX = 100; // 向右拖拽 100px
      const containerWidth = 1000; // 容器宽度 1000px
      const minLeftWidth = 200;
      const maxLeftWidth = 800;

      const newLeftWidthPercent = calculateNewLeftWidth(
        startLeftWidthPercent,
        deltaX,
        containerWidth,
        minLeftWidth,
        maxLeftWidth
      );

      // 50% of 1000px = 500px, +100px = 600px, 600/1000 = 60%
      expect(newLeftWidthPercent).toBe(60);
    });

    it('应该在两列模式中遵守最小左列宽度限制', () => {
      const startLeftWidthPercent = 30; // 30%
      const deltaX = -200; // 向左拖拽 200px
      const containerWidth = 1000;
      const minLeftWidth = 200; // 最小 200px = 20%
      const maxLeftWidth = 800;

      const newLeftWidthPercent = calculateNewLeftWidth(
        startLeftWidthPercent,
        deltaX,
        containerWidth,
        minLeftWidth,
        maxLeftWidth
      );

      // 30% of 1000px = 300px, -200px = 100px, 但被限制在最小值 200px = 20%
      expect(newLeftWidthPercent).toBe(20);
    });

    it('应该在两列模式中遵守最大左列宽度限制', () => {
      const startLeftWidthPercent = 70; // 70%
      const deltaX = 200; // 向右拖拽 200px
      const containerWidth = 1000;
      const minLeftWidth = 200;
      const maxLeftWidth = 800; // 最大 800px = 80%

      const newLeftWidthPercent = calculateNewLeftWidth(
        startLeftWidthPercent,
        deltaX,
        containerWidth,
        minLeftWidth,
        maxLeftWidth
      );

      // 70% of 1000px = 700px, +200px = 900px, 但被限制在最大值 800px = 80%
      expect(newLeftWidthPercent).toBe(80);
    });

    it('应该支持向后兼容的 leftCol 和 rightCol 属性', () => {
      const backwardCompatibleProps: ResizableContainerProps = {
        leftCol: 'Left column content',
        rightCol: 'Right column content',
        height: 400
      };

      expect(backwardCompatibleProps.leftCol).toBe('Left column content');
      expect(backwardCompatibleProps.rightCol).toBe('Right column content');
    });

    it('应该优先使用新的属性名而不是旧的属性名', () => {
      // 这个测试验证组件逻辑中 leftContent 优先于 leftCol
      const mixedProps: ResizableContainerProps = {
        leftContent: 'New left content',
        leftCol: 'Old left content',
        rightContent: 'New right content',
        rightCol: 'Old right content'
      };

      // 在实际组件中，应该使用 leftContent 而不是 leftCol
      expect(mixedProps.leftContent).toBe('New left content');
      expect(mixedProps.rightContent).toBe('New right content');
    });

    it('应该有合理的两列模式默认值', () => {
      const defaultTwoColumnProps = {
        initialLeftWidth: '50%',
        minLeftWidth: 200,
        maxLeftWidth: 800
      };

      expect(parseInitialLeftWidth(defaultTwoColumnProps.initialLeftWidth)).toBe(50);
      expect(defaultTwoColumnProps.minLeftWidth).toBeLessThan(defaultTwoColumnProps.maxLeftWidth);
      expect(defaultTwoColumnProps.minLeftWidth).toBeGreaterThan(0);
    });
  });
});
