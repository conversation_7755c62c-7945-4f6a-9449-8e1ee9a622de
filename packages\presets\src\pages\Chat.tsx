import React, { useEffect, useMemo } from "react";

import { NavigationBar } from "@/components";
import { DefaultChatLayout } from "@/layout";
import { useConversationState, useParams } from "@cscs-agent/core";
import {
  BuildInCommand,
  MessageContainer,
  Sender,
  SidePanel,
  useActiveConversationId,
  useCommandRunner,
  useMessages,
} from "@cscs-agent/core";

const Chat: React.FC = () => {
  const params = useParams();
  const [, setActiveConversationId] = useActiveConversationId();
  const runner = useCommandRunner();
  const [, setMessages] = useMessages();
  const { clearState } = useConversationState();

  useEffect(() => {
    setActiveConversationId(params.id ?? null);

    return () => {
      setActiveConversationId(null);
      setMessages([]);
      runner(BuildInCommand.CancelChatRequest);
      clearState();
    };
  }, [params.id]);

  const senderMemo = useMemo(() => {
    return <Sender />;
  }, []);

  const messageContainer = useMemo(() => <MessageContainer />, []);

  return (
    <DefaultChatLayout
      navigationBar={<NavigationBar />}
      message={messageContainer}
      sender={senderMemo}
      sidePanel={
        <div className="pts:bg-gray-100" style={{ height: "100vh" }}>
          <SidePanel />
        </div>
      }
    />
  );
};

export default Chat;
